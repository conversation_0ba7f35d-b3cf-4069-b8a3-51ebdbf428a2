package auth

import (
	"context"
	"mime/multipart"
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/auth"
	"github.com/labstack/echo/v4"
	"golang.org/x/crypto/bcrypt"
)

// MockAuthService implements the auth service interface for testing
type MockAuthService struct {
	otps map[string]*auth.OTP
}

func NewMockAuthService() *MockAuthService {
	return &MockAuthService{
		otps: make(map[string]*auth.OTP),
	}
}

func (m *MockAuthService) GenerateOTP(ctx context.Context, phone string) error {
	// Simulate OTP generation
	if phone == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "phone number is required")
	}

	// Store a mock OTP
	hashedOTP, _ := bcrypt.GenerateFromPassword([]byte("123456"), bcrypt.DefaultCost)
	m.otps[phone] = &auth.OTP{
		Phone: phone,
		Code:  string(hashedOTP),
	}

	return nil
}

func (m *MockAuthService) VerifyOTP(ctx context.Context, phone, code string) (string, error) {
	// Simulate OTP verification
	if phone == "" || code == "" {
		return "", echo.NewHTTPError(http.StatusBadRequest, "phone and code are required")
	}

	// Check if OTP exists
	if otp, exists := m.otps[phone]; exists {
		// Verify code
		if err := bcrypt.CompareHashAndPassword([]byte(otp.Code), []byte(code)); err == nil {
			// Delete OTP after successful verification
			delete(m.otps, phone)
			// Return mock access token
			return "mock-access-token-" + phone, nil
		}
	}

	return "", echo.NewHTTPError(http.StatusUnauthorized, "Invalid or expired verification code")
}

func (m *MockAuthService) GetStatusByPhone(ctx context.Context, phone string) (string, string, error) {
	if _, exists := m.otps[phone]; exists {
		return "PENDING_VERIFICATION", "", nil
	}
	return "UNKNOWN", "", nil
}

// Implement other required methods (not used in tests)
func (m *MockAuthService) Register(ctx context.Context, user *model.User, photo *multipart.FileHeader, referralCode string) (*token.Token, error) {
	return nil, nil
}

func (m *MockAuthService) LegacyRegister(ctx context.Context, user *model.User, referralCode string) (*token.Token, error) {
	return nil, nil
}

func (m *MockAuthService) Login(ctx context.Context, user *model.User) (*model.User, *token.Token, error) {
	return nil, nil, nil
}

func (m *MockAuthService) RefreshAuth(ctx context.Context, tokenStr string) (*model.User, *token.Token, error) {
	return nil, nil, nil
}

func (m *MockAuthService) ForgotPassword(ctx context.Context, email string) error {
	return nil
}

func (m *MockAuthService) ResetPassword(ctx context.Context, tokenStr, password string) error {
	return nil
}

func (m *MockAuthService) CheckPassword(ctx context.Context, userID, password string) error {
	return nil
}

func (m *MockAuthService) AdminLogin(ctx context.Context, user *model.User) (*token.Token, error) {
	return nil, nil
}

func (m *MockAuthService) HRLogin(ctx context.Context, user *model.User) (*token.Token, error) {
	return nil, nil
}
