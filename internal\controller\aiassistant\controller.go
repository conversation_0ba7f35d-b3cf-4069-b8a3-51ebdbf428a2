package aiassistant

import (
	"context"
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/service/aiassistant"
	"github.com/labstack/echo/v4"
)

// Controller defines the interface for the AI Assistant controller
type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// Handlers
	FinancialHealthOverview() echo.HandlerFunc       // New AI Assistant API
	DreamProgressAnalysis() echo.HandlerFunc         // Dream Progress Analysis API
	LearningPathData() echo.HandlerFunc              // Learning Path Data API
	SpendingAnalysisData() echo.HandlerFunc          // Spending Analysis Data API
	InvestmentProfileData() echo.HandlerFunc         // Investment Profile Data API
	FinancialIndependenceData() echo.HandlerFunc     // Financial Independence Data API
	GamificationEngagementData() echo.HandlerFunc    // Gamification Engagement Data API
	FamilyFinancialDNAData() echo.HandlerFunc        // Family Financial DNA Data API
	TransactionBehaviorData() echo.HandlerFunc       // Transaction Behavior Data API
	ComprehensiveFinancialContext() echo.HandlerFunc // Comprehensive Financial Context API
}

// controller implements the Controller interface
type controller struct {
	Service aiassistant.Service
}

// New creates a new instance of the AI Assistant controller
func New(service aiassistant.Service) Controller {
	return &controller{
		Service: service,
	}
}

// RegisterRoutes registers all routes for the AI Assistant controller
func (c *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	aiAssistantChain := []echo.MiddlewareFunc{
		middlewares.AuthGuard(),
		middlewares.UserContextMiddleware(),
		middlewares.N8nGuard(), // Added N8nGuard middleware for service authentication
	}

	// Create a group for AI assistant endpoints
	aiAssistantGroup := currentGroup.Group("/aiassistant")

	// Register routes with service authentication middleware
	aiAssistantGroup.GET("/financial-health-overview", c.FinancialHealthOverview(), aiAssistantChain...)             // Financial Health Overview API
	aiAssistantGroup.GET("/dream-progress-analysis", c.DreamProgressAnalysis(), aiAssistantChain...)                 // Dream Progress Analysis API
	aiAssistantGroup.GET("/learning-path-data", c.LearningPathData(), aiAssistantChain...)                           // Learning Path Data API
	aiAssistantGroup.GET("/spending-analysis-data", c.SpendingAnalysisData(), aiAssistantChain...)                   // Spending Analysis Data API
	aiAssistantGroup.GET("/investment-profile-data", c.InvestmentProfileData(), aiAssistantChain...)                 // Investment Profile Data API
	aiAssistantGroup.GET("/financial-independence-data", c.FinancialIndependenceData(), aiAssistantChain...)         // Financial Independence Data API
	aiAssistantGroup.GET("/gamification-engagement-data", c.GamificationEngagementData(), aiAssistantChain...)       // Gamification Engagement Data API
	aiAssistantGroup.GET("/family-financial-dna-data", c.FamilyFinancialDNAData(), aiAssistantChain...)              // Family Financial DNA Data API
	aiAssistantGroup.GET("/transaction-behavior-data", c.TransactionBehaviorData(), aiAssistantChain...)             // Transaction Behavior Data API
	aiAssistantGroup.GET("/comprehensive-financial-context", c.ComprehensiveFinancialContext(), aiAssistantChain...) // Comprehensive Financial Context API
}

// FinancialHealthOverview handles the GET /v2/aiassistant/financial-health-overview endpoint
func (c *controller) FinancialHealthOverview() echo.HandlerFunc {
	return func(e echo.Context) error {
		ctx := e.Request().Context()

		// Extract user ID from JWT token (set by AuthGuard middleware)
		userToken, err := token.GetClaimsFromRequest(e.Request())
		if err != nil {
			return errors.New(errors.Controller, "failed to extract user claims", errors.Unauthorized, err)
		}

		// Call service to fetch financial health overview data
		financialHealthData, err := c.Service.FinancialHealthOverview(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Return the financial health overview data
		return e.JSON(http.StatusOK, financialHealthData)
	}
}

// DreamProgressAnalysis handles the GET /v2/aiassistant/dream-progress-analysis endpoint
func (c *controller) DreamProgressAnalysis() echo.HandlerFunc {
	return func(e echo.Context) error {
		ctx := e.Request().Context()

		// Extract user ID from JWT token (set by AuthGuard middleware)
		userToken, err := token.GetClaimsFromRequest(e.Request())
		if err != nil {
			return errors.New(errors.Controller, "failed to extract user claims", errors.Unauthorized, err)
		}

		// Call service to fetch dream progress analysis data
		dreamProgressData, err := c.Service.DreamProgressAnalysis(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Return the dream progress analysis data
		return e.JSON(http.StatusOK, dreamProgressData)
	}
}

// LearningPathData provides comprehensive learning path data
func (c *controller) LearningPathData() echo.HandlerFunc {
	return func(e echo.Context) error {
		ctx := e.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(e.Request())
		if err != nil {
			return err
		}

		// Call service to fetch learning path data
		learningPathData, err := c.Service.LearningPathData(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Return the learning path data
		return e.JSON(http.StatusOK, learningPathData)
	}
}

// SpendingAnalysisData provides comprehensive spending analysis data
func (c *controller) SpendingAnalysisData() echo.HandlerFunc {
	return func(e echo.Context) error {
		ctx := e.Request().Context()

		// Extract user ID from JWT token (set by AuthGuard middleware)
		userToken, err := token.GetClaimsFromRequest(e.Request())
		if err != nil {
			return errors.New(errors.Controller, "failed to extract user claims", errors.Unauthorized, err)
		}

		// Call service to fetch spending analysis data
		spendingAnalysisData, err := c.Service.SpendingAnalysisData(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Return the spending analysis data
		return e.JSON(http.StatusOK, spendingAnalysisData)
	}
}

// InvestmentProfileData provides comprehensive investment profile data
func (c *controller) InvestmentProfileData() echo.HandlerFunc {
	return func(e echo.Context) error {
		ctx := e.Request().Context()

		// Extract user ID from JWT token (set by AuthGuard middleware)
		userToken, err := token.GetClaimsFromRequest(e.Request())
		if err != nil {
			return errors.New(errors.Controller, "failed to extract user claims", errors.Unauthorized, err)
		}

		// Call service to fetch investment profile data
		investmentProfileData, err := c.Service.InvestmentProfileData(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Return the investment profile data
		return e.JSON(http.StatusOK, investmentProfileData)
	}
}

// FinancialIndependenceData handles the GET /v2/aiassistant/financial-independence-data endpoint
func (c *controller) FinancialIndependenceData() echo.HandlerFunc {
	return func(e echo.Context) error {
		ctx := e.Request().Context()

		// Extract user ID from JWT token (set by AuthGuard middleware)
		userToken, err := token.GetClaimsFromRequest(e.Request())
		if err != nil {
			return errors.New(errors.Controller, "failed to extract user claims", errors.Unauthorized, err)
		}

		// Call service to fetch financial independence data
		financialIndependenceData, err := c.Service.FinancialIndependenceData(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Return the financial independence data
		return e.JSON(http.StatusOK, financialIndependenceData)
	}
}

// GamificationEngagementData handles the GET /v2/aiassistant/gamification-engagement-data endpoint
func (c *controller) GamificationEngagementData() echo.HandlerFunc {
	return func(e echo.Context) error {
		ctx := e.Request().Context()

		// Extract user ID from JWT token (set by AuthGuard middleware)
		userToken, err := token.GetClaimsFromRequest(e.Request())
		if err != nil {
			return errors.New(errors.Controller, "failed to extract user claims", errors.Unauthorized, err)
		}

		// Call service to fetch gamification engagement data
		gamificationEngagementData, err := c.Service.GamificationEngagementData(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Return the gamification engagement data
		return e.JSON(http.StatusOK, gamificationEngagementData)
	}
}

// FamilyFinancialDNAData handles the GET /v2/aiassistant/family-financial-dna-data endpoint
func (c *controller) FamilyFinancialDNAData() echo.HandlerFunc {
	return func(e echo.Context) error {
		ctx := e.Request().Context()

		// Extract user ID from JWT token (set by AuthGuard middleware)
		userToken, err := token.GetClaimsFromRequest(e.Request())
		if err != nil {
			return errors.New(errors.Controller, "failed to extract user claims", errors.Unauthorized, err)
		}

		// Call service to fetch family financial DNA data
		familyFinancialDNAData, err := c.Service.FamilyFinancialDNAData(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Return the family financial DNA data
		return e.JSON(http.StatusOK, familyFinancialDNAData)
	}
}

// TransactionBehaviorData handles the GET /v2/aiassistant/transaction-behavior-data endpoint
func (c *controller) TransactionBehaviorData() echo.HandlerFunc {
	return func(e echo.Context) error {
		ctx := e.Request().Context()

		// Extract user ID from JWT token (set by AuthGuard middleware)
		userToken, err := token.GetClaimsFromRequest(e.Request())
		if err != nil {
			return errors.New(errors.Controller, "failed to extract user claims", errors.Unauthorized, err)
		}

		// Call service to fetch transaction behavior data
		transactionBehaviorData, err := c.Service.TransactionBehaviorData(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Return the transaction behavior data
		return e.JSON(http.StatusOK, transactionBehaviorData)
	}
}

// ComprehensiveFinancialContext handles the GET /v2/aiassistant/comprehensive-financial-context endpoint
func (c *controller) ComprehensiveFinancialContext() echo.HandlerFunc {
	return func(e echo.Context) error {
		ctx := e.Request().Context()

		// Extract user ID from JWT token (set by AuthGuard middleware)
		userToken, err := token.GetClaimsFromRequest(e.Request())
		if err != nil {
			return errors.New(errors.Controller, "failed to extract user claims", errors.Unauthorized, err)
		}

		// Call service to fetch comprehensive financial context data
		comprehensiveFinancialContext, err := c.Service.ComprehensiveFinancialContext(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Return the comprehensive financial context data
		return e.JSON(http.StatusOK, comprehensiveFinancialContext)
	}
}
