package auth

import (
	"context"
	"testing"

	"github.com/dsoplabs/dinbora-backend/internal/model/auth"
	"golang.org/x/crypto/bcrypt"
)

// MockOTPRepository implements the OTP repository interface for testing
type MockOTPRepository struct {
	otps map[string]*auth.OTP
}

func NewMockOTPRepository() *MockOTPRepository {
	return &MockOTPRepository{
		otps: make(map[string]*auth.OTP),
	}
}

func (m *MockOTPRepository) FindByPhone(ctx context.Context, phone string) (*auth.OTP, error) {
	if otp, exists := m.otps[phone]; exists {
		return otp, nil
	}
	return nil, nil
}

func (m *MockOTPRepository) FindValidByPhone(ctx context.Context, phone string) (*auth.OTP, error) {
	if otp, exists := m.otps[phone]; exists && otp.IsValid() {
		return otp, nil
	}
	return nil, nil
}

func (m *MockOTPRepository) FindByPhoneAndCode(ctx context.Context, phone, code string) (*auth.OTP, error) {
	if otp, exists := m.otps[phone]; exists {
		if err := bcrypt.CompareHashAndPassword([]byte(otp.Code), []byte(code)); err == nil {
			return otp, nil
		}
	}
	return nil, nil
}

func (m *MockOTPRepository) Create(ctx context.Context, otp *auth.OTP) error {
	m.otps[otp.Phone] = otp
	return nil
}

func (m *MockOTPRepository) Update(ctx context.Context, otp *auth.OTP) error {
	m.otps[otp.Phone] = otp
	return nil
}

func (m *MockOTPRepository) Upsert(ctx context.Context, otp *auth.OTP) error {
	m.otps[otp.Phone] = otp
	return nil
}

func (m *MockOTPRepository) Delete(ctx context.Context, id string) error {
	for phone, otp := range m.otps {
		if otp.ID == id {
			delete(m.otps, phone)
			break
		}
	}
	return nil
}

func (m *MockOTPRepository) DeleteByPhone(ctx context.Context, phone string) error {
	delete(m.otps, phone)
	return nil
}

func (m *MockOTPRepository) DeleteExpired(ctx context.Context) error {
	for phone, otp := range m.otps {
		if otp.IsExpired() {
			delete(m.otps, phone)
		}
	}
	return nil
}

func TestGenerateOTPCore(t *testing.T) {
	// Test the core OTP generation logic without email dependencies
	service := &service{}

	// Test OTP generation
	otp, err := service.generateSecureOTP()
	if err != nil {
		t.Fatalf("generateSecureOTP failed: %v", err)
	}

	if len(otp) != 6 {
		t.Errorf("Expected 6-digit OTP, got %s", otp)
	}

	// Test OTP hashing
	hashedOTP, err := service.hashOTP(otp)
	if err != nil {
		t.Fatalf("hashOTP failed: %v", err)
	}

	if hashedOTP == "" {
		t.Error("Hashed OTP should not be empty")
	}

	// Verify the hash can be verified
	err = bcrypt.CompareHashAndPassword([]byte(hashedOTP), []byte(otp))
	if err != nil {
		t.Errorf("Hash verification failed: %v", err)
	}
}

func TestGenerateOTPInvalidPhone(t *testing.T) {
	service := &service{}

	ctx := context.Background()

	// Test empty phone
	err := service.GenerateOTP(ctx, "")
	if err == nil {
		t.Error("Expected error for empty phone")
	}

	// Test invalid phone format
	err = service.GenerateOTP(ctx, "123")
	if err == nil {
		t.Error("Expected error for invalid phone format")
	}
}

func TestGenerateSecureOTP(t *testing.T) {
	service := &service{}

	// Generate multiple OTPs to test randomness
	otps := make(map[string]bool)
	for i := 0; i < 100; i++ {
		otp, err := service.generateSecureOTP()
		if err != nil {
			t.Fatalf("generateSecureOTP failed: %v", err)
		}

		// Verify OTP format
		if len(otp) != 6 {
			t.Errorf("Expected 6-digit OTP, got %s", otp)
		}

		// Check for duplicates (should be very rare)
		if otps[otp] {
			t.Logf("Duplicate OTP generated: %s (this is rare but possible)", otp)
		}
		otps[otp] = true
	}

	// Verify we got some variety (at least 50% unique)
	if len(otps) < 50 {
		t.Errorf("Expected more variety in OTP generation, got %d unique OTPs out of 100", len(otps))
	}
}

func TestHashOTP(t *testing.T) {
	service := &service{}

	otp := "123456"
	hashedOTP, err := service.hashOTP(otp)
	if err != nil {
		t.Fatalf("hashOTP failed: %v", err)
	}

	if hashedOTP == "" {
		t.Error("Hashed OTP should not be empty")
	}

	if hashedOTP == otp {
		t.Error("Hashed OTP should be different from original")
	}

	// Verify the hash can be verified
	err = bcrypt.CompareHashAndPassword([]byte(hashedOTP), []byte(otp))
	if err != nil {
		t.Errorf("Hash verification failed: %v", err)
	}
}
