package financialplan

import (
	"context"
	"sort"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialplan"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	_dreamboard "github.com/dsoplabs/dinbora-backend/internal/repository/dreamboard"
	_financialsheet "github.com/dsoplabs/dinbora-backend/internal/service/financialsheet"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Service interface {
	// Transactions CRUD
	FindAllTransactions(ctx context.Context, userID string, categoryType financialsheet.CategoryType, period int) ([]*financialsheet.Transaction, error)

	// Utility
	Summary(ctx context.Context, userID string, period int) (*financialplan.Summary, error)
}

type service struct {
	FinancialSheetService _financialsheet.Service
	DreamboardRepository  _dreamboard.Repository
}

func New(financialSheetService _financialsheet.Service, dreamboardRepository _dreamboard.Repository) Service {
	return &service{
		FinancialSheetService: financialSheetService,
		DreamboardRepository:  dreamboardRepository,
	}
}

func (s *service) FindAllTransactions(ctx context.Context, userID string, categoryType financialsheet.CategoryType, period int) ([]*financialsheet.Transaction, error) {
	// Get the current date
	now := time.Now()

	// Calculate the start date based on the period
	startDate := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.UTC)

	// Calculate the end date based on the period
	nextPeriodStart := startDate.AddDate(0, period, 0)
	endDate := nextPeriodStart.AddDate(0, 0, -1)

	// Fetch transactions within the period
	transactions, err := s.FinancialSheetService.FindAllTransactionsMonthsInRange(ctx, userID, categoryType, startDate, endDate)
	if err != nil {
		return nil, err
	}

	// Filter networth tracking transactions
	filteredNetworthTransactions, err := s.filterNetworthTrackingTransactions(transactions)
	if err != nil {
		return nil, err
	}

	var filteredDreamsTransactions []*financialsheet.Transaction = filteredNetworthTransactions // Copy the slice to avoid mutating the original slice
	// For the financial plan, we need to remove the dreams transaction and include the monthly savings/pledged amount in a dummy transaction
	if categoryType == financialsheet.CategoryTypeCostsOfLiving {
		filteredDreamsTransactions, err = s.filterDreamsTransactions(ctx, userID, filteredNetworthTransactions, startDate, endDate)
		if err != nil {
			return nil, err
		}
	}

	// Order the transactions by date
	sort.Slice(filteredDreamsTransactions, func(i, j int) bool {
		return filteredDreamsTransactions[i].Date.Before(filteredDreamsTransactions[j].Date)
	})

	return filteredDreamsTransactions, nil
}

func (s *service) Summary(ctx context.Context, userID string, period int) (*financialplan.Summary, error) {
	// Get the current date
	now := time.Now()

	// Calculate the start date based on the period
	startDate := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.UTC)

	// Calculate the end date based on the period
	nextPeriodStart := startDate.AddDate(0, period, 0)
	endDate := nextPeriodStart.AddDate(0, 0, -1)

	// Fetch transactions within the period
	transactions, err := s.FinancialSheetService.FindAllTransactionsMonthsInRange(ctx, userID, "", startDate, endDate) // Empty category type means all
	if err != nil {
		return nil, err
	}

	// Filter networth tracking transactions
	filteredNetworthTransactions, err := s.filterNetworthTrackingTransactions(transactions)
	if err != nil {
		return nil, err
	}

	// For the financial plan, we need to remove the dreams transaction and include the monthly savings/pledged amount in a dummy transaction
	filteredDreamsTransactions, err := s.filterDreamsTransactions(ctx, userID, filteredNetworthTransactions, startDate, endDate)
	if err != nil {
		return nil, err
	}

	// Calculate the summary
	return calculateSummary(filteredDreamsTransactions)
}

func (s *service) filterNetworthTrackingTransactions(transactions []*financialsheet.Transaction) ([]*financialsheet.Transaction, error) {
	// Remove all hidden transactions
	n := 0 // The index of the next element to keep

	for _, transaction := range transactions {
		// If we want to keep this transaction...
		if !transaction.Hidden {
			// ...copy it to the n-th position.
			transactions[n] = transaction
			n++ // and advance the write index.
		}
	}

	// The first 'n' elements are the ones we kept.
	// We return a new slice header pointing to just those elements.
	return transactions[:n], nil
}

func (s *service) filterDreamsTransactions(ctx context.Context, userID string, transactions []*financialsheet.Transaction, startDate time.Time, endDate time.Time) ([]*financialsheet.Transaction, error) {
	// --- Step 1: Efficiently filter out existing dream transactions ---
	// Create a new slice, pre-allocating capacity for the transactions we're keeping.
	// This avoids modifying the slice while iterating (the bug) and is more efficient.
	filteredTransactions := make([]*financialsheet.Transaction, 0, len(transactions))
	for _, transaction := range transactions {
		if transaction.Category != financialsheet.CategoryIdentifierDreams {
			filteredTransactions = append(filteredTransactions, transaction)
		}
	}

	// --- Step 2: Fetch data from the repository ---
	dreams, err := s.DreamboardRepository.FindDreamsByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if len(dreams) == 0 {
		return filteredTransactions, nil // No dreams, nothing more to do
	}

	contributions, err := s.DreamboardRepository.FindContributionsByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// --- Step 3: Create a lookup map for contributions (O(1) access) ---
	// This is the key performance optimization. Instead of a nested loop,
	// we create a map to find a contribution by its DreamID instantly.
	contributionMap := make(map[string]*dreamboard.Contribution, len(contributions))
	for _, c := range contributions {
		contributionMap[c.DreamID] = c
	}

	// --- Step 4: Generate new dream transactions and add to the result ---
	// We'll append the new transactions to our 'filteredTransactions' slice.
	for _, dream := range dreams {
		if dream.Completed {
			continue // Skip completed dreams
		}

		var dreamValue monetary.Amount
		if dream.IsShared {
			// Instant lookup from the map - much faster!
			if contribution, ok := contributionMap[dream.ID]; ok {
				dreamValue = contribution.MonthlyPledgedAmount
			} else {
				// No contribution found for this shared dream, skip it or handle as an error
				continue
			}
		} else {
			dreamValue = dream.MonthlySavings
		}

		// If there's no value to add, no need to create transactions
		if dreamValue == 0 {
			continue
		}

		// Create monthly transactions for this dream
		for date := startDate; date.Before(dream.Deadline) && date.Before(endDate); date = date.AddDate(0, 1, 0) {
			transaction := &financialsheet.Transaction{
				ObjectID:          primitive.NewObjectID(),
				Category:          financialsheet.CategoryIdentifierDreams,
				MoneySource:       financialsheet.MoneySourceOther,
				Value:             dreamValue,
				Date:              date,
				AttachedDreamID:   dream.ID,
				AttachedDreamName: dream.Title,
				Type:              financialsheet.CategoryTypeCostsOfLiving,
			}
			filteredTransactions = append(filteredTransactions, transaction)
		}
	}

	return filteredTransactions, nil
}

func calculateSummary(transactions []*financialsheet.Transaction) (*financialplan.Summary, error) {
	// Initialize summary
	summary := &financialplan.Summary{
		TotalIncome:        0,
		TotalCostsOfLiving: 0,
		TotalExpenses:      0,
		Balance:            0,
	}

	// Calculate totals
	for _, transaction := range transactions {
		switch transaction.Type {
		case financialsheet.CategoryTypeIncome:
			summary.TotalIncome += transaction.Value
		case financialsheet.CategoryTypeCostsOfLiving:
			summary.TotalCostsOfLiving += transaction.Value
		case financialsheet.CategoryTypeExpense:
			summary.TotalExpenses += transaction.Value
		}
	}

	// Calculate balance
	summary.Balance = summary.TotalIncome - (summary.TotalCostsOfLiving + summary.TotalExpenses)

	return summary, nil
}
