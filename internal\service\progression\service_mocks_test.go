package progression

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"github.com/stretchr/testify/mock"
)

//region MockProgressionRepo

// Mock progression repository
type MockProgressionRepo struct {
	mock.Mock
}

func (m *MockProgressionRepo) CreateEvent(ctx context.Context, event *progression.ProgressEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *MockProgressionRepo) CreateEventsBatch(ctx context.Context, events []*progression.ProgressEvent) error {
	args := m.Called(ctx, events)
	return args.Error(0)
}

func (m *MockProgressionRepo) GetUserEvents(ctx context.Context, userID string, limit int) ([]*progression.ProgressEvent, error) {
	args := m.Called(ctx, userID, limit)

	// Handle dynamic return value from a function
	if fn, ok := args.Get(0).(func(context.Context, string, int) []*progression.ProgressEvent); ok {
		return fn(ctx, userID, limit), args.Error(1)
	}

	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*progression.ProgressEvent), args.Error(1)
}

func (m *MockProgressionRepo) GetTrailEvents(ctx context.Context, userID string, trailID string) ([]*progression.ProgressEvent, error) {
	args := m.Called(ctx, userID, trailID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*progression.ProgressEvent), args.Error(1)
}

func (m *MockProgressionRepo) GetEventsByTimeRange(ctx context.Context, userID string, start, end time.Time) ([]*progression.ProgressEvent, error) {
	args := m.Called(ctx, userID, start, end)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*progression.ProgressEvent), args.Error(1)
}

func (m *MockProgressionRepo) GetProgressSummary(ctx context.Context, userID string) (*progression.ProgressSummary, error) {
	args := m.Called(ctx, userID)

	// Handle dynamic return value from a function
	if fn, ok := args.Get(0).(func(context.Context, string) *progression.ProgressSummary); ok {
		return fn(ctx, userID), args.Error(1)
	}

	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*progression.ProgressSummary), args.Error(1)
}

func (m *MockProgressionRepo) GetProgressSummaryBatch(ctx context.Context, userIds []string) ([]*progression.ProgressSummary, error) {
	args := m.Called(ctx, userIds)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*progression.ProgressSummary), args.Error(1)
}

func (m *MockProgressionRepo) SaveProgressSummary(ctx context.Context, summary *progression.ProgressSummary) error {
	args := m.Called(ctx, summary)
	return args.Error(0)
}

func (m *MockProgressionRepo) InvalidateProgressSummary(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockProgressionRepo) MarkUserMigrated(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockProgressionRepo) FindByUser(ctx context.Context, userId string) (*progression.Progression, error) {
	args := m.Called(ctx, userId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*progression.Progression), args.Error(1)
}

//endregion MockProgressionRepo

// region MockTrailSvc
// Mock trail service
type MockTrailSvc struct {
	mock.Mock
}

func (m *MockTrailSvc) Find(ctx context.Context, id string) (*content.Trail, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Trail), args.Error(1)
}

func (m *MockTrailSvc) FindAll(ctx context.Context) ([]*content.Trail, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*content.Trail), args.Error(1)
}

func (m *MockTrailSvc) FindByIdentifier(ctx context.Context, identifier string) (*content.Trail, error) {
	args := m.Called(ctx, identifier)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Trail), args.Error(1)
}

func (m *MockTrailSvc) FindCardData(ctx context.Context, id string) (*content.TrailCard, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.TrailCard), args.Error(1)
}

func (m *MockTrailSvc) FindAllCardData(ctx context.Context) ([]*content.TrailCard, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*content.TrailCard), args.Error(1)
}

func (m *MockTrailSvc) CreateRegularTrail(ctx context.Context, trail *content.Trail) error {
	args := m.Called(ctx, trail)
	return args.Error(0)
}

func (m *MockTrailSvc) FindRegularTrail(ctx context.Context, id string) (*content.Trail, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Trail), args.Error(1)
}

func (m *MockTrailSvc) FindAllRegularTrails(ctx context.Context) ([]*content.Trail, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*content.Trail), args.Error(1)
}

func (m *MockTrailSvc) FindRegularTrailByIdentifier(ctx context.Context, identifier string) (*content.Trail, error) {
	args := m.Called(ctx, identifier)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Trail), args.Error(1)
}

func (m *MockTrailSvc) UpdateRegularTrail(ctx context.Context, trail *content.Trail) error {
	args := m.Called(ctx, trail)
	return args.Error(0)
}

func (m *MockTrailSvc) DeleteRegularTrail(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockTrailSvc) FindRegularTrailCardData(ctx context.Context, id string) (*content.TrailCard, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.TrailCard), args.Error(1)
}

func (m *MockTrailSvc) FindAllRegularTrailsCardData(ctx context.Context) ([]*content.TrailCard, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*content.TrailCard), args.Error(1)
}

func (m *MockTrailSvc) CreateRegularLesson(ctx context.Context, trailID string, lesson *content.Lesson) error {
	args := m.Called(ctx, trailID, lesson)
	return args.Error(0)
}

func (m *MockTrailSvc) FindRegularLesson(ctx context.Context, trailID string, lessonID string) (*content.Lesson, error) {
	args := m.Called(ctx, trailID, lessonID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Lesson), args.Error(1)
}

func (m *MockTrailSvc) UpdateRegularLesson(ctx context.Context, trailID string, lessonID string, lesson *content.Lesson) error {
	args := m.Called(ctx, trailID, lessonID, lesson)
	return args.Error(0)
}

func (m *MockTrailSvc) DeleteRegularLesson(ctx context.Context, trailID string, lessonID string) error {
	args := m.Called(ctx, trailID, lessonID)
	return args.Error(0)
}

func (m *MockTrailSvc) CreateExtraTrail(ctx context.Context, trail *content.Trail) error {
	args := m.Called(ctx, trail)
	return args.Error(0)
}

func (m *MockTrailSvc) FindExtraTrail(ctx context.Context, id string, userClassification string) (*content.Trail, error) {
	args := m.Called(ctx, id, userClassification)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Trail), args.Error(1)
}

func (m *MockTrailSvc) FindAllExtraTrails(ctx context.Context, userClassification string) ([]*content.Trail, error) {
	args := m.Called(ctx, userClassification)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*content.Trail), args.Error(1)
}

func (m *MockTrailSvc) FindExtraTrailByIdentifier(ctx context.Context, identifier string, userClassification string) (*content.Trail, error) {
	args := m.Called(ctx, identifier, userClassification)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Trail), args.Error(1)
}

func (m *MockTrailSvc) UpdateExtraTrail(ctx context.Context, trail *content.Trail) error {
	args := m.Called(ctx, trail)
	return args.Error(0)
}

func (m *MockTrailSvc) DeleteExtraTrail(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockTrailSvc) FindExtraTrailCardData(ctx context.Context, id string, userClassification string) (*content.TrailCard, error) {
	args := m.Called(ctx, id, userClassification)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.TrailCard), args.Error(1)
}

func (m *MockTrailSvc) FindAllExtraTrailsCardData(ctx context.Context, userClassification string) ([]*content.TrailCard, error) {
	args := m.Called(ctx, userClassification)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*content.TrailCard), args.Error(1)
}

func (m *MockTrailSvc) CreateExtraLesson(ctx context.Context, trailID string, userClassification string, lesson *content.Lesson) error {
	args := m.Called(ctx, trailID, userClassification, lesson)
	return args.Error(0)
}

func (m *MockTrailSvc) FindExtraLesson(ctx context.Context, trailID string, userClassification string, lessonID string) (*content.Lesson, error) {
	args := m.Called(ctx, trailID, userClassification, lessonID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Lesson), args.Error(1)
}

func (m *MockTrailSvc) UpdateExtraLesson(ctx context.Context, trailID string, userClassification string, lessonID string, lesson *content.Lesson) error {
	args := m.Called(ctx, trailID, userClassification, lessonID, lesson)
	return args.Error(0)
}

func (m *MockTrailSvc) DeleteExtraLesson(ctx context.Context, trailID string, userClassification string, lessonID string) error {
	args := m.Called(ctx, trailID, userClassification, lessonID)
	return args.Error(0)
}

func (m *MockTrailSvc) CreateTutorial(ctx context.Context, tutorial *content.Tutorial) error {
	args := m.Called(ctx, tutorial)
	return args.Error(0)
}

func (m *MockTrailSvc) FindTutorial(ctx context.Context, id string) (*content.Tutorial, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Tutorial), args.Error(1)
}

func (m *MockTrailSvc) FindAllTutorials(ctx context.Context) ([]*content.Tutorial, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*content.Tutorial), args.Error(1)
}

func (m *MockTrailSvc) FindTutorialByIdentifier(ctx context.Context, identifier string) (*content.Tutorial, error) {
	args := m.Called(ctx, identifier)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Tutorial), args.Error(1)
}

func (m *MockTrailSvc) FindTutorialByTicker(ctx context.Context, ticker string) (*content.Tutorial, error) {
	args := m.Called(ctx, ticker)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Tutorial), args.Error(1)
}

func (m *MockTrailSvc) UpdateTutorial(ctx context.Context, tutorial *content.Tutorial) error {
	args := m.Called(ctx, tutorial)
	return args.Error(0)
}

func (m *MockTrailSvc) DeleteTutorial(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockTrailSvc) CreateTutorialLesson(ctx context.Context, tutorialID string, lesson *content.Lesson) error {
	args := m.Called(ctx, tutorialID, lesson)
	return args.Error(0)
}

func (m *MockTrailSvc) FindTutorialLesson(ctx context.Context, tutorialID string, lessonID string) (*content.Lesson, error) {
	args := m.Called(ctx, tutorialID, lessonID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Lesson), args.Error(1)
}

func (m *MockTrailSvc) UpdateTutorialLesson(ctx context.Context, tutorialID string, lessonID string, lesson *content.Lesson) error {
	args := m.Called(ctx, tutorialID, lessonID, lesson)
	return args.Error(0)
}

func (m *MockTrailSvc) DeleteTutorialLesson(ctx context.Context, tutorialID string, lessonID string) error {
	args := m.Called(ctx, tutorialID, lessonID)
	return args.Error(0)
}

//endregion MockTrailSvc

//region MockVaultSvc

// Mock vault service
type MockVaultSvc struct {
	mock.Mock
}

func (m *MockVaultSvc) FindVaultsByUsers(ctx context.Context, userIds []string) (map[string]*model.Vault, error) {
	args := m.Called(ctx, userIds)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]*model.Vault), args.Error(1)
}

func (m *MockVaultSvc) Reward(ctx context.Context, userId string, coins int) error {
	args := m.Called(ctx, userId, coins)
	return args.Error(0)
}

func (m *MockVaultSvc) Find(ctx context.Context, id string) (*model.Vault, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Vault), args.Error(1)
}

func (m *MockVaultSvc) FindByUser(ctx context.Context, userId string) (*model.Vault, error) {
	args := m.Called(ctx, userId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Vault), args.Error(1)
}

func (m *MockVaultSvc) Create(ctx context.Context, vault *model.Vault) error {
	args := m.Called(ctx, vault)
	return args.Error(0)
}

func (m *MockVaultSvc) Update(ctx context.Context, vault *model.Vault) error {
	args := m.Called(ctx, mock.Anything)
	return args.Error(0)
}

func (m *MockVaultSvc) Delete(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockVaultSvc) Initialize(ctx context.Context, userId string, referred bool) error {
	args := m.Called(ctx, userId, referred)
	return args.Error(0)
}

//endregion MockVaultSvc

//region MockAchievementSvc

// Mock achievement service
type MockAchievementSvc struct {
	mock.Mock
}

func (m *MockAchievementSvc) FindByRequirement(ctx context.Context, requirement string) (*content.Achievement, error) {
	args := m.Called(ctx, requirement)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Achievement), args.Error(1)
}

func (m *MockAchievementSvc) Find(ctx context.Context, id string) (*content.Achievement, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Achievement), args.Error(1)
}

func (m *MockAchievementSvc) FindAll(ctx context.Context) ([]*content.Achievement, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*content.Achievement), args.Error(1)
}

func (m *MockAchievementSvc) FindByIdentifier(ctx context.Context, identifier string) (*content.Achievement, error) {
	args := m.Called(ctx, identifier)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Achievement), args.Error(1)
}

func (m *MockAchievementSvc) Create(ctx context.Context, achievement *content.Achievement) error {
	args := m.Called(ctx, achievement)
	return args.Error(0)
}

func (m *MockAchievementSvc) Update(ctx context.Context, achievement *content.Achievement) error {
	args := m.Called(ctx, achievement)
	return args.Error(0)
}

func (m *MockAchievementSvc) Delete(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

//endregion MockAchievementSvc

//region MockCacheSvc

// Mock cache service
type MockCacheSvc struct {
	mock.Mock
}

func (m *MockCacheSvc) Get(ctx context.Context, key string) (interface{}, bool) {
	args := m.Called(ctx, key)
	return args.Get(0), args.Bool(1)
}

func (m *MockCacheSvc) Set(ctx context.Context, key string, value interface{}, duration time.Duration) error {
	args := m.Called(ctx, key, value, duration)
	return args.Error(0)
}

func (m *MockCacheSvc) Delete(ctx context.Context, key string) error {
	args := m.Called(ctx, key)
	return args.Error(0)
}

//endregion MockCacheSvc
