Of course. This is a critical task, and having a clear, step-by-step plan is essential for success.

Here is a comprehensive implementation and migration plan, designed to be as safe and seamless as possible.

---

### **Phase 1: Preparation & Setup (No Production Code Changes)**

The goal of this phase is to prepare the new database schema and the necessary code structures without affecting the current application.

**Step 1: Finalize Go Structs for the New Schema**
Confirm you have the new Go structs in your project that map to the new collections.

```go
// models/category.go
type Category struct {
    ID         primitive.ObjectID `bson:"_id,omitempty"`
    Identifier string             `bson:"identifier"`
    Name       string             `bson:"name"`
    Icon       string             `bson:"icon"`
    Color      string             `bson:"color"`
}

// models/dream.go
type Dream struct {
    ID           primitive.ObjectID `bson:"_id,omitempty"`
    DreamboardID primitive.ObjectID `bson:"dreamboardId"`
    UserID       string             `bson:"userId"`
    CategoryID   primitive.ObjectID `bson:"categoryId"`
    // ... all other dream fields
}

// models/dreamboard.go
type Dreamboard struct {
    ID              primitive.ObjectID `bson:"_id,omitempty"`
    UserID          string             `bson:"user"`
    TotalDreamsCost int64              `bson:"totalDreamsCost"`
    // ... all other summary fields
}
```

**Step 2: Create and Populate the `categories` Collection**
This is a one-time setup. Since categories are static, you can pre-populate them. Create a simple script or run this directly in the `mongosh` shell.

```javascript
// In mongosh:
db.createCollection("categories")

db.categories.insertMany([
    { identifier: "professional", name: "Profissional", icon: "...", color: "#BEFFE1" },
    { identifier: "financial", name: "Financeiro", icon: "...", color: "#F6E4FF" },
    { identifier: "leisure", name: "Lazer", icon: "...", color: "#A4FFE6" },
    { identifier: "emotional", name: "Emocional", icon: "...", color: "#E4E9FF" },
    { identifier: "intellectual", name: "Intelectual", icon: "...", color: "#F9FFFA" },
    { identifier: "spiritual", name: "Espiritual", icon: "...", color: "#FFD9D9" },
    { identifier: "physical", name: "Físico", icon: "...", color: "#D0EAFF" },
    { identifier: "intimate", name: "Amoroso", icon: "...", color: "#F6E4FF" },
    { identifier: "social", name: "Social", icon: "...", color: "#BEFFE1" },
    { identifier: "familial", name: "Familiar", icon: "...", color: "#F0EAFF" }
]);

// Create an index for fast lookups by the migration script
db.categories.createIndex({ "identifier": 1 });
```

**Step 3: Create Empty `dreams` and `dreamboards` Collections**
You can create these ahead of time so they are ready for the migration.

```javascript
db.createCollection("dreams")
db.createCollection("dreamboards")
```

---

### **Phase 2: The Migration Script**

This is a standalone Go program you will write and run once to migrate the data.

**SAFETY FIRST: Before running the script, take a full backup of your production database.**

**Step 1: Create a Migration Project Folder**
Create a new folder, e.g., `/migration`, inside your project. This will contain the migration logic.

**Step 2: Define a Struct for the Old Data**
The script needs to be able to read the old, embedded document structure.

```go
// migration/main.go

// OldDream represents the embedded dream structure
type OldDream struct {
	ID        primitive.ObjectID `bson:"_id"`
	Category  int                `bson:"category"` // This is the old integer index
    Color     string             `bson:"color"`
	Title     string             `bson:"title"`
    // ... all other fields from the old dream object
}

// OldDreamboard represents the entire old document
type OldDreamboard struct {
	ID         primitive.ObjectID `bson:"_id"`
	User       string             `bson:"user"`
	Categories []struct {
		ID         primitive.ObjectID `bson:"_id"`
		Identifier string             `bson:"identifier"`
	} `bson:"categories"`
	Dreams          []OldDream `bson:"dreams"`
	TotalDreamsCost int64      `bson:"totalDreamsCost"`
    // ... all other fields from the old dreamboard
}
```

**Step 3: Write the Migration Logic**
This script will read from `dreamboards_old` (rename your current collection) and write to the new `dreamboards` and `dreams` collections.

```go
// migration/main.go
package main

import (
	"context"
	"fmt"
	"log"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	// ... import your new model structs
)

func main() {
	// --- 1. Connect to MongoDB ---
	client, err := mongo.Connect(context.TODO(), options.Client().ApplyURI("your_mongo_uri"))
	if err != nil {
		log.Fatal(err)
	}
	defer client.Disconnect(context.TODO())
	
	db := client.Database("your_db_name")
	oldCollection := db.Collection("dreamboards_old") // RENAME your current collection to this
	newDreamboardsCollection := db.Collection("dreamboards")
	newDreamsCollection := db.Collection("dreams")
	categoriesCollection := db.Collection("categories")

	// --- 2. Fetch all new categories and create a lookup map ---
	// This map will translate the old 'identifier' to the new Category ObjectID
	categoryMap := make(map[string]primitive.ObjectID)
	cursor, err := categoriesCollection.Find(context.TODO(), bson.D{})
    if err != nil { log.Fatal(err) }
	var categories []models.Category
    if err = cursor.All(context.TODO(), &categories); err != nil { log.Fatal(err) }
    for _, cat := range categories {
        categoryMap[cat.Identifier] = cat.ID
    }
	log.Printf("Loaded %d categories into lookup map.", len(categoryMap))

	// --- 3. Iterate through all old dreamboards ---
	cursor, err = oldCollection.Find(context.TODO(), bson.D{})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(context.TODO())

	log.Println("Starting migration...")
	var boardsMigrated int
	for cursor.Next(context.TODO()) {
		var oldBoard OldDreamboard
		if err := cursor.Decode(&oldBoard); err != nil {
			log.Printf("Error decoding old board: %v", err)
			continue
		}

		// --- 4. Create and Insert the new Dreamboard document ---
		newBoard := models.Dreamboard{
			ID:              oldBoard.ID, // Preserve the original ID
			UserID:          oldBoard.User,
			TotalDreamsCost: oldBoard.TotalDreamsCost,
            // ... copy other summary fields
		}
		_, err := newDreamboardsCollection.InsertOne(context.TODO(), newBoard)
		if err != nil {
			log.Printf("Failed to insert new dreamboard for user %s: %v", oldBoard.User, err)
			continue
		}

		// --- 5. Transform and collect all dreams for this board ---
		if len(oldBoard.Dreams) == 0 {
            log.Printf("User %s has no dreams to migrate.", oldBoard.User)
			boardsMigrated++
			continue
		}

		dreamsToInsert := make([]interface{}, 0, len(oldBoard.Dreams))
		for _, oldDream := range oldBoard.Dreams {
			
            // **IMPORTANT MAPPING LOGIC**
            // The old `category` field is an integer index. We map it back to an identifier.
            // This assumes the order in old `categories` array is stable.
            var categoryIdentifier string
            if oldDream.Category > 0 && oldDream.Category <= len(oldBoard.Categories) {
                 categoryIdentifier = oldBoard.Categories[oldDream.Category-1].Identifier
            } else {
                log.Printf("Warning: Invalid category index %d for dream '%s'", oldDream.Category, oldDream.Title)
                continue // Skip this dream
            }

            newCategoryID, ok := categoryMap[categoryIdentifier]
            if !ok {
                log.Printf("Warning: Category identifier '%s' not found in map for dream '%s'", categoryIdentifier, oldDream.Title)
                continue // Skip this dream
            }

			newDream := models.Dream{
				ID:           oldDream.ID, // Preserve original ID
				DreamboardID: newBoard.ID,
				UserID:       newBoard.UserID,
				CategoryID:   newCategoryID,
				// ... copy all other fields from oldDream to newDream
			}
			dreamsToInsert = append(dreamsToInsert, newDream)
		}

		// --- 6. Bulk insert the new dreams ---
		if len(dreamsToInsert) > 0 {
			_, err = newDreamsCollection.InsertMany(context.TODO(), dreamsToInsert)
			if err != nil {
				log.Printf("Failed to insert dreams for dreamboard %s: %v", newBoard.ID.Hex(), err)
				// Consider adding rollback logic for the parent dreamboard here if needed
			}
		}
        boardsMigrated++
		log.Printf("Successfully migrated dreamboard for user %s with %d dreams.", oldBoard.User, len(dreamsToInsert))
	}
	log.Printf("Migration complete! %d boards processed.", boardsMigrated)
}
```

---

### **Phase 3: Application Code Refactoring**

Now, update your application to use the new schema.

**Step 1: Create the DTO Struct**
Create the `DreamboardDTO` struct as discussed, which mimics the old API response.

**Step 2: Update the Repository Layer**
Create new data access functions.
*   `GetDreamboardByID(id) (*models.Dreamboard, error)`
*   `GetDreamsByDreamboardID(boardID) ([]*models.Dream, error)`
*   `GetCategories() ([]*models.Category, error)` (can be cached)
*   `CreateDream(*models.Dream)` - **This must also update the parent `Dreamboard` summary fields.**
*   `UpdateDream(*models.Dream)` - **Also update summary fields.**
*   `DeleteDream(id)` - **Also update summary fields.**

**Step 3: Update the Controller/Handler (The "Assembler")**
Modify your API handlers to use the new repository functions and build the DTO.

```go
func GetDreamboardHandler(w http.ResponseWriter, r *http.Request) {
    // 1. Get Dreamboard by ID from the new repository function
    dreamboard, err := repository.GetDreamboardByID(...)
    
    // 2. Get all Dreams for that dreamboard
    dreams, err := repository.GetDreamsByDreamboardID(dreamboard.ID)

    // 3. Get all Categories (from cache or DB)
    categories, err := repository.GetCategories()

    // 4. Assemble the DreamboardDTO from the 3 pieces of data
    responseDTO := assembleDreamboardDTO(dreamboard, dreams, categories)

    // 5. Send the DTO as the JSON response
    json.NewEncoder(w).Encode(responseDTO)
}
```

---

### **Phase 4: Deployment & Cleanup**

**Step 1: Rename the old collection**
In your production database, rename the current `dreamboards` collection.
`db.dreamboards.renameCollection("dreamboards_old")`

**Step 2: Deploy the new Application Code**
The new code is now live. It will work with the new `dreamboards` and `dreams` collections, which are currently empty. New users will use the new system correctly. Old users will not find their data (this is expected for a brief period).

**Step 3: Run the Migration Script**
Execute your compiled Go migration program (`./migration`). It will read from `dreamboards_old` and populate the new `dreamboards` and `dreams` collections. Monitor the logs for any errors.

**Step 4: Verify**
Once the script is done, old users' data should now be visible and working correctly through the new application code. Perform thorough testing.

**Step 5: Cleanup (After a Safe Period)**
After you are confident that the migration was successful and everything is stable (e.g., after one week), you can drop the old collection.
`db.dreamboards_old.drop()`

## Instructions
I need to update the dreamboard structure due to performance, the dreams and categories inside the dreamboard will now be in ther own collection. 
The collection "dreamboards.dreams" for dreams and "dreamboards.categories" for categories. To do that we need some changes.

### **Phase 1: Preparation & Setup (No Production Code Changes)**
Confirm you have the new Go structs that map to the new collections. The main update is the "dream" struct will now have the DreamboardID field.

**Step 2: Create and Populate the `categories` Collection**
The "migration" folder is design to this. Adding a folder in "/migration/dreamboards.categories" with the categories.json file. The migrator package will
automatically load the categories.json file and insert the data into the "dreamboards.categories" collection. You just need to create the json files.

**Step 3: Create Empty `dreamboards`, `dreamboards.dreams` and `dreamboards.categories` Collections**
You can create these ahead of time so they are ready for the migration

### **Phase 2: The Migration**
Create a migration cable of migrating the current dreamboards to the new structure. We should store the old dreamboards in a new collection "dreamboards_old" and then run the migration.
You can check the "migrator" package or look the "/migration" folder for more information. Migrate all dreams to the new collection "dreamboards.dreams".

### **Phase 3: Application Code Refactoring**

Now, update your application to use the new schema. The goal of this is to keep the API response the same as before. Now the controller will be responsible
to build the "DreamboardDTO" struct that will be returned to the client, with the same response as before. 

**Step 1: Create the DTO Struct**

**Step 2: Update the Repository Layer**
Create new data access functions.
*   `GetDreamboardByID(id) (*models.Dreamboard, error)`
*   `GetDreamsByDreamboardID(boardID) ([]*models.Dream, error)`
*   `GetCategories() ([]*models.Category, error)` (can be cached)
*   `CreateDream(*models.Dream)` - **This must also update the parent `Dreamboard` summary fields. Which means some information in the dreamboard still needs to be calculated check the "CalculateSavedAmount" function in the "dreamboard.go" file.**
*   `UpdateDream(*models.Dream)` - **Also update summary fields.**
*   `DeleteDream(id)` - **Also update summary fields.**

**Step 3: Update the Controller/Handler (The "Assembler")**

**Step 1: Create the DTO Struct**
Create the `DreamboardDTO` struct as discussed, which mimics the old API response.

**Step 2: Update the Repository Layer**
Create new data access functions.
*   `GetDreamboardByID(id) (*models.Dreamboard, error)`
*   `GetDreamsByDreamboardID(boardID) ([]*models.Dream, error)`
*   `GetCategories() ([]*models.Category, error)` (can be cached)
*   `CreateDream(*models.Dream)` - **This must also update the parent `Dreamboard` summary fields.**
*   `UpdateDream(*models.Dream)` - **Also update summary fields.**
*   `DeleteDream(id)` - **Also update summary fields.**

**Step 3: Update the Controller/Handler (The "Assembler")**
Modify your API handlers to use the new repository functions and build the DTO.