package financialsheet

import (
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// TestCategoryDataIntegrity_ProductionScenario tests the exact production scenario
// This test reproduces the bug where custom categories were being reset to zero
func TestCategoryDataIntegrity_ProductionScenario(t *testing.T) {
	// This test focuses on the core issue: updateCategoryIDs method
	// We'll test it in isolation to avoid complex mock dependencies

	// Arrange - Simulate the production scenario
	service := &service{}

	// User categories from production (new_category_1, new_category_2)
	userCategories := []*financialsheet.Category{
		{
			ID:         "prod-cat-1-id",
			User:       "prod-user",
			Identifier: "new_category_1",
			Type:       financialsheet.CategoryTypeExpense,
			Name:       "Custom Category 1",
		},
		{
			ID:         "prod-cat-2-id",
			User:       "prod-user",
			Identifier: "new_category_2",
			Type:       financialsheet.CategoryTypeExpense,
			Name:       "Custom Category 2",
		},
	}

	// Month data with existing category values (like in production before the bug)
	monthData := financialsheet.MonthData{
		Categories: []financialsheet.CategoryCard{
			// Predefined categories
			{
				ID:         "comp-id",
				Identifier: string(financialsheet.CategoryIdentifierCompensation),
				Name:       "Remuneração",
				Value:      5000,
			},
			{
				ID:         "housing-id",
				Identifier: string(financialsheet.CategoryIdentifierHousing),
				Name:       "Moradia",
				Value:      1200,
			},
			// Custom categories with actual transaction values
			{
				ID:         "old-cat-1-id", // This should be updated to prod-cat-1-id
				Identifier: "new_category_1",
				Name:       "Custom Category 1",
				Value:      150, // This value should be preserved
			},
			{
				ID:         "old-cat-2-id", // This should be updated to prod-cat-2-id
				Identifier: "new_category_2",
				Name:       "Custom Category 2",
				Value:      250, // This value should be preserved
			},
		},
	}

	// Act - Call the method that was causing the bug
	service.updateCategoryIDs(monthData, userCategories)

	// Assert - Verify that the fix works correctly
	customCat1Found := false
	customCat2Found := false

	for _, card := range monthData.Categories {
		switch card.Identifier {
		case "new_category_1":
			customCat1Found = true
			assert.Equal(t, "prod-cat-1-id", card.ID, "Custom category 1 ID should be updated correctly")
			assert.Equal(t, monetary.Amount(150), card.Value, "Custom category 1 value should be preserved")
		case "new_category_2":
			customCat2Found = true
			assert.Equal(t, "prod-cat-2-id", card.ID, "Custom category 2 ID should be updated correctly")
			assert.Equal(t, monetary.Amount(250), card.Value, "Custom category 2 value should be preserved")
		case string(financialsheet.CategoryIdentifierCompensation):
			// Predefined categories should remain unchanged
			assert.Equal(t, "comp-id", card.ID, "Predefined category ID should remain unchanged")
			assert.Equal(t, monetary.Amount(5000), card.Value, "Predefined category value should be preserved")
		case string(financialsheet.CategoryIdentifierHousing):
			assert.Equal(t, "housing-id", card.ID, "Predefined category ID should remain unchanged")
			assert.Equal(t, monetary.Amount(1200), card.Value, "Predefined category value should be preserved")
		}
	}

	assert.True(t, customCat1Found, "Custom category 1 should be found")
	assert.True(t, customCat2Found, "Custom category 2 should be found")
}

// TestUpdateCategoryIDs_IdentifierBasedMapping tests the fixed updateCategoryIDs method
func TestUpdateCategoryIDs_IdentifierBasedMapping(t *testing.T) {
	// Arrange
	service := &service{}

	userCategories := []*financialsheet.Category{
		{
			ID:         "user-cat-1",
			Identifier: "new_category_1",
			Name:       "User Category 1",
		},
		{
			ID:         "user-cat-2",
			Identifier: "new_category_2",
			Name:       "User Category 2",
		},
	}

	monthData := financialsheet.MonthData{
		Categories: []financialsheet.CategoryCard{
			// Predefined category
			{
				ID:         "old-comp-id",
				Identifier: string(financialsheet.CategoryIdentifierCompensation),
				Name:       "Remuneração",
				Value:      1000,
			},
			// User categories with old/incorrect IDs
			{
				ID:         "old-id-1",
				Identifier: "new_category_1",
				Name:       "User Category 1",
				Value:      150,
			},
			{
				ID:         "old-id-2",
				Identifier: "new_category_2",
				Name:       "User Category 2",
				Value:      250,
			},
		},
	}

	// Act
	service.updateCategoryIDs(monthData, userCategories)

	// Assert - verify that IDs were updated correctly based on identifier matching
	for _, card := range monthData.Categories {
		switch card.Identifier {
		case "new_category_1":
			assert.Equal(t, "user-cat-1", card.ID, "Category 1 ID should be updated to match user category")
			assert.Equal(t, monetary.Amount(150), card.Value, "Category 1 value should be preserved")
		case "new_category_2":
			assert.Equal(t, "user-cat-2", card.ID, "Category 2 ID should be updated to match user category")
			assert.Equal(t, monetary.Amount(250), card.Value, "Category 2 value should be preserved")
		case string(financialsheet.CategoryIdentifierCompensation):
			assert.Equal(t, "old-comp-id", card.ID, "Predefined category ID should remain unchanged")
			assert.Equal(t, monetary.Amount(1000), card.Value, "Predefined category value should be preserved")
		}
	}
}

// TestBuildFromCategories_CreatesZeroValues tests that buildFromCategories creates categories with zero values
func TestBuildFromCategories_CreatesZeroValues(t *testing.T) {
	// This test verifies the behavior of buildFromCategories method
	// It should create categories with zero values, which is expected behavior for new months

	// Arrange
	service := &service{}

	userCategories := []*financialsheet.Category{
		{
			ID:         "user-cat-1",
			User:       "test-user",
			Identifier: "new_category_1",
			Type:       financialsheet.CategoryTypeExpense,
			Name:       "Custom Category 1",
			Icon:       financialsheet.CategoryIconOther,
			Background: financialsheet.CategoryBackgroundExpense,
		},
	}

	// Act
	result := service.buildFromCategories(userCategories)

	// Assert
	assert.NotNil(t, result.Categories)

	// Should include predefined categories + user categories
	predefinedCount := len(financialsheet.GetAllCategories())
	userCategoryCount := 1 // We have 1 user category
	expectedTotal := predefinedCount + userCategoryCount

	assert.Equal(t, expectedTotal, len(result.Categories), "Should include all predefined categories plus user categories")

	// Find the custom category
	var customCatFound bool
	for _, card := range result.Categories {
		if card.Identifier == "new_category_1" {
			customCatFound = true
			assert.Equal(t, "user-cat-1", card.ID, "Custom category ID should be set correctly")
			assert.Equal(t, monetary.Amount(0), card.Value, "Custom category should start with zero value")
			assert.Equal(t, "Custom Category 1", card.Name, "Custom category name should be preserved")
			break
		}
	}

	assert.True(t, customCatFound, "Custom category should be found in result")

	// Verify all categories have zero values (this is expected for new months)
	for _, card := range result.Categories {
		assert.Equal(t, monetary.Amount(0), card.Value, "All categories should start with zero values in buildFromCategories")
	}
}

// TestProductionBugFix_September2024_CustomCategories tests the exact production bug scenario
// This test demonstrates that the fix resolves the issue where custom categories
// "new_category_1" and "new_category_2" were being reset to zero in September 2024 data
func TestProductionBugFix_September2024_CustomCategories(t *testing.T) {
	// This test simulates the exact production scenario that was reported:
	// - User has custom categories "new_category_1" and "new_category_2"
	// - Categories had transaction values but were mysteriously reset to zero
	// - The path "yearData.09.categories.new_category_2" was missing from the document

	// Arrange - Recreate the production scenario
	service := &service{}

	// Production user categories
	userCategories := []*financialsheet.Category{
		{
			ID:         "66f1a2b3c4d5e6f7a8b9c0d1", // Simulated production ID
			User:       "production-user-id",
			Identifier: "new_category_1",
			Type:       financialsheet.CategoryTypeExpense,
			Name:       "Custom Category 1",
			Icon:       financialsheet.CategoryIconOther,
			Background: financialsheet.CategoryBackgroundExpense,
		},
		{
			ID:         "66f1a2b3c4d5e6f7a8b9c0d2", // Simulated production ID
			User:       "production-user-id",
			Identifier: "new_category_2",
			Type:       financialsheet.CategoryTypeExpense,
			Name:       "Custom Category 2",
			Icon:       financialsheet.CategoryIconOther,
			Background: financialsheet.CategoryBackgroundExpense,
		},
	}

	// September 2024 month data with existing category aggregations
	// This represents the state BEFORE the bug occurred
	septemberMonthData := financialsheet.MonthData{
		Categories: []financialsheet.CategoryCard{
			// Some predefined categories
			{
				ID:         "predefined-comp-id",
				Identifier: string(financialsheet.CategoryIdentifierCompensation),
				Name:       "Remuneração",
				Value:      8500, // User's salary
			},
			{
				ID:         "predefined-housing-id",
				Identifier: string(financialsheet.CategoryIdentifierHousing),
				Name:       "Moradia",
				Value:      2200, // User's rent
			},
			// Custom categories with actual transaction values (this is what was being lost)
			{
				ID:         "old-incorrect-id-1", // This would be corrected by updateCategoryIDs
				Identifier: "new_category_1",
				Name:       "Custom Category 1",
				Value:      450, // Sum of transactions for this category
			},
			{
				ID:         "old-incorrect-id-2", // This would be corrected by updateCategoryIDs
				Identifier: "new_category_2",
				Name:       "Custom Category 2",
				Value:      320, // Sum of transactions for this category
			},
		},
		Transactions: []financialsheet.Transaction{
			// Transactions that created the category aggregations
			{
				ObjectID: primitive.NewObjectID(),
				Category: "new_category_1",
				Value:    200,
				Date:     time.Date(2024, 9, 5, 0, 0, 0, 0, time.UTC),
			},
			{
				ObjectID: primitive.NewObjectID(),
				Category: "new_category_1",
				Value:    250,
				Date:     time.Date(2024, 9, 15, 0, 0, 0, 0, time.UTC),
			},
			{
				ObjectID: primitive.NewObjectID(),
				Category: "new_category_2",
				Value:    320,
				Date:     time.Date(2024, 9, 20, 0, 0, 0, 0, time.UTC),
			},
		},
	}

	// Act - Apply the fixed updateCategoryIDs method
	// This simulates what happens when the system processes the data
	service.updateCategoryIDs(septemberMonthData, userCategories)

	// Assert - Verify the fix prevents data loss
	customCat1Found := false
	customCat2Found := false
	var customCat1Value, customCat2Value monetary.Amount

	for _, card := range septemberMonthData.Categories {
		switch card.Identifier {
		case "new_category_1":
			customCat1Found = true
			customCat1Value = card.Value
			// Verify ID was correctly updated
			assert.Equal(t, "66f1a2b3c4d5e6f7a8b9c0d1", card.ID, "new_category_1 ID should be correctly mapped")
		case "new_category_2":
			customCat2Found = true
			customCat2Value = card.Value
			// Verify ID was correctly updated
			assert.Equal(t, "66f1a2b3c4d5e6f7a8b9c0d2", card.ID, "new_category_2 ID should be correctly mapped")
		}
	}

	// Critical assertions - these would have failed before the fix
	assert.True(t, customCat1Found, "new_category_1 should be preserved in September data")
	assert.True(t, customCat2Found, "new_category_2 should be preserved in September data")
	assert.Equal(t, monetary.Amount(450), customCat1Value, "new_category_1 aggregated value should be preserved (not reset to zero)")
	assert.Equal(t, monetary.Amount(320), customCat2Value, "new_category_2 aggregated value should be preserved (not reset to zero)")

	// Verify that the path "yearData.09.categories.new_category_2" would NOT be missing
	// (This was the specific issue mentioned in the production report)
	assert.NotEqual(t, monetary.Amount(0), customCat2Value, "new_category_2 should not be zero (path should not be missing)")

	t.Logf("✅ Production bug fix verified:")
	t.Logf("   - new_category_1 value preserved: %d", customCat1Value)
	t.Logf("   - new_category_2 value preserved: %d", customCat2Value)
	t.Logf("   - Category IDs correctly mapped using identifier-based logic")
	t.Logf("   - No data loss occurred during category processing")
}
