package session

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/auth"
)

type Reader interface {
	FindByPhone(ctx context.Context, phone string) (*auth.Session, error)
	FindByToken(ctx context.Context, token string) (*auth.Session, error)
	FindValidByPhone(ctx context.Context, phone string) (*auth.Session, error)
}

type Writer interface {
	Create(ctx context.Context, session *auth.Session) error
	Update(ctx context.Context, session *auth.Session) error
	Delete(ctx context.Context, id string) error
	DeleteByPhone(ctx context.Context, phone string) error
	DeleteExpired(ctx context.Context) error
}

type Repository interface {
	Reader
	Writer
}
