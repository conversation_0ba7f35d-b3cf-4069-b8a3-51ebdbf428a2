package financialsheet

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Reader interface {
	// Read operations
	Find(ctx context.Context, id primitive.ObjectID) (*financialsheet.Record, error)
	FindAll(ctx context.Context) ([]*financialsheet.Record, error)
	FindByUser(ctx context.Context, userID string) (*financialsheet.Record, error)
	FindByUsers(ctx context.Context, userIDs []string) (map[string]*financialsheet.Record, error)

	// Category Read operations
	FindCategory(ctx context.Context, id primitive.ObjectID) (*financialsheet.Category, error)
	FindAllCategories(ctx context.Context) ([]*financialsheet.Category, error)
	FindCategoryByUserAndIdentifier(ctx context.Context, userID string, identifier financialsheet.CategoryIdentifier) (*financialsheet.Category, error)

	// Transactions Read operations
	FindAllTransactions(ctx context.Context, userID string, categoryType financialsheet.CategoryType, year, month int) ([]*financialsheet.Transaction, error)
	FindAllTransactionsMonthsInRange(ctx context.Context, userID string, categoryType financialsheet.CategoryType, startMonth, endMonth time.Time) ([]*financialsheet.Transaction, error)
	FindTransactionMonthsInRange(ctx context.Context, userID string, startMonth, endMonth time.Time) (map[string]bool, error)
}

type Writer interface {
	// Create operations
	Create(ctx context.Context, record *financialsheet.Record) (string, error)

	// Update operations
	Update(ctx context.Context, record *financialsheet.Record) error

	// Delete operations
	Delete(ctx context.Context, id primitive.ObjectID) error

	// Category Management
	CreateCategory(ctx context.Context, category *financialsheet.Category) (string, error)
	UpdateCategory(ctx context.Context, category *financialsheet.Category) error
	DeleteCategory(ctx context.Context, id primitive.ObjectID) error
}

type Repository interface {
	Reader
	Writer
}
