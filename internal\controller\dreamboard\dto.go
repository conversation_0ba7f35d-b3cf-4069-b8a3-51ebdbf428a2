package dreamboard

import (
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Request DTOs

// Implement the Requests later
// CreateDreamRequest represents the request to create a dream
type CreateDreamRequest struct {
	Category          string          `json:"category" validate:"required"`
	Title             string          `json:"title" validate:"required,min=1,max=100"`
	TimeFrame         string          `json:"timeFrame" validate:"required"`
	Deadline          string          `json:"deadline" validate:"required"`
	EstimatedCost     monetary.Amount `json:"estimatedCost" validate:"required,min=1"`
	MonthlySavings    monetary.Amount `json:"monthlySavings" validate:"required,min=1"`
	MoneySource       []string        `json:"moneySource" validate:"required"`
	CustomMoneySource string          `json:"customMoneySource,omitempty"`
	IsShared          bool            `json:"isShared"`
}

// UpdateDreamRequest represents the request to update a dream
type UpdateDreamRequest struct {
	Category          string          `json:"category"`
	Title             string          `json:"title" validate:"min=1,max=100"`
	TimeFrame         string          `json:"timeFrame"`
	Deadline          string          `json:"deadline"`
	EstimatedCost     monetary.Amount `json:"estimatedCost" validate:"min=1"`
	MonthlySavings    monetary.Amount `json:"monthlySavings" validate:"min=1"`
	MoneySource       []string        `json:"moneySource"`
	CustomMoneySource string          `json:"customMoneySource,omitempty"`
	Completed         *bool           `json:"completed,omitempty"`
}

// CreateCategoryRequest represents the request to create a category
type CreateCategoryRequest struct {
	Identifier string `json:"identifier" validate:"required"`
	Name       string `json:"name" validate:"required"`
	Icon       string `json:"icon" validate:"required"`
	Color      string `json:"color" validate:"required"`
}

// UpdateCategoryRequest represents the request to update a category
type UpdateCategoryRequest struct {
	Identifier string `json:"identifier"`
	Name       string `json:"name"`
	Icon       string `json:"icon"`
	Color      string `json:"color"`
}

// JoinSharedDreamRequest represents the request to join a shared dream
type JoinSharedDreamRequest struct {
	Code                 string          `json:"code" validate:"required"`
	MonthlyPledgedAmount monetary.Amount `json:"monthlyPledgedAmount" validate:"required,min=1"`
}

// UpdateContributionRequest represents the request to update a contribution
type UpdateContributionRequest struct {
	MonthlyPledgedAmount monetary.Amount               `json:"monthlyPledgedAmount" validate:"min=1"`
	Status               dreamboard.ContributionStatus `json:"status"`
}

// UpdateShareLinkStatusRequest represents the request to update share link status
type UpdateShareLinkStatusRequest struct {
	IsEnabled bool `json:"isEnabled"`
}

// UpdateContributionStatusRequest represents the request to update contribution status
type UpdateContributionStatusRequest struct {
	Status dreamboard.ContributionStatus `json:"status" validate:"required"`
}

// Response DTOs

// DreamboardDTO represents the response structure for API backward compatibility
// This DTO assembles data from multiple collections (dreamboards, dreams, categories)
// to maintain the same API response format as the previous embedded structure
type DreamboardDTO struct {
	ObjectID        primitive.ObjectID     `json:"-" bson:"_id,omitempty"`
	ID              string                 `json:"id,omitempty" bson:"-"`
	User            string                 `json:"user" bson:"user"`
	Categories      []*dreamboard.Category `json:"categories" bson:"categories"`
	Dreams          []*dreamboard.Dream    `json:"dreams" bson:"dreams"`
	TotalDreamsCost monetary.Amount        `json:"totalDreamsCost" bson:"totalDreamsCost"`
	SavedAmount     monetary.Amount        `json:"savedAmount"`
	MonthlyNeeded   monetary.Amount        `json:"monthlyNeeded" bson:"monthlyNeeded"`
	RemainingAmount monetary.Amount        `json:"remainingAmount" bson:"remainingAmount"`
	CreatedAt       time.Time              `json:"createdAt" bson:"createdAt"`
	UpdatedAt       time.Time              `json:"updatedAt" bson:"updatedAt"`
}

// FromDreamboard creates a DreamboardDTO from a Dreamboard and associated data
func (dto *DreamboardDTO) FromDreamboard(dreamboard *dreamboard.Dreamboard, dreams []*dreamboard.Dream, categories []*dreamboard.Category) {
	dto.ObjectID = dreamboard.ObjectID
	dto.ID = dreamboard.ID
	dto.User = dreamboard.User
	dto.Categories = categories
	dto.Dreams = dreams
	dto.TotalDreamsCost = dreamboard.TotalDreamsCost
	dto.SavedAmount = dreamboard.SavedAmount
	dto.MonthlyNeeded = dreamboard.MonthlyNeeded
	dto.RemainingAmount = dreamboard.RemainingAmount
	dto.CreatedAt = dreamboard.CreatedAt
	dto.UpdatedAt = dreamboard.UpdatedAt
}

// ToDreamboard converts DTO back to Dreamboard model (without embedded data)
func (dto *DreamboardDTO) ToDreamboard() *dreamboard.Dreamboard {
	return &dreamboard.Dreamboard{
		ObjectID:        dto.ObjectID,
		ID:              dto.ID,
		User:            dto.User,
		TotalDreamsCost: dto.TotalDreamsCost,
		SavedAmount:     dto.SavedAmount,
		MonthlyNeeded:   dto.MonthlyNeeded,
		RemainingAmount: dto.RemainingAmount,
		CreatedAt:       dto.CreatedAt,
		UpdatedAt:       dto.UpdatedAt,
	}
}

// ComputeTotals calculates and updates the total costs and savings fields for the DTO
func (dto *DreamboardDTO) ComputeTotals(dreams []*dreamboard.Dream) {
	var totalCost, monthlySum monetary.Amount
	for _, dream := range dreams {
		// Only consider active dreams
		if !dream.Completed {
			totalCost += dream.EstimatedCost
			monthlySum += dream.MonthlySavings
		}
	}
	dto.TotalDreamsCost = totalCost
	dto.MonthlyNeeded = monthlySum
	dto.RemainingAmount = dto.SavedAmount - dto.TotalDreamsCost
}
