# Verify OTP API

## Overview

The Verify OTP API validates a one-time-use code for phone authentication, creates a user session, and returns an access token for authenticated requests.

## Endpoint

**POST** `/api/v2/auth/verify-otp`

## Security

This endpoint is **protected** by the N8nGuard middleware, requiring a valid API key for access.

## Request

### Headers
- `Content-Type: application/json`
- `X-API-Key: <your-api-key>` (required for N8nGuard)

### Body
```json
{
  "phone": "31991445883",
  "code": "123456"
}
```

### Parameters
- `phone` (string, required): The phone number associated with the OTP
- `code` (string, required): The 6-digit OTP code received via email

## Response

### Success Response
**Status Code:** `200 OK`

```json
{
  "status": "success",
  "message": "Verification successful.",
  "access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOi..."
}
```

### Error Responses

#### Invalid or Expired Code
**Status Code:** `401 Unauthorized`

```json
{
  "error": "Invalid or expired verification code."
}
```

#### Missing Required Fields
**Status Code:** `400 Bad Request`

```json
{
  "error": "Phone number and code are required."
}
```

#### Internal Server Error
**Status Code:** `500 Internal Server Error`

```json
{
  "error": "OTP verification failed."
}
```

## Backend Logic

When the backend receives a request at `POST /api/v2/auth/verify-otp`, it performs these steps:

1. **Validate Input**: Ensures both phone and code are present in the request
2. **Find Pending OTP**: Queries the database for a valid OTP record matching the phone number
3. **Handle Not Found**: If no record exists, returns generic 401 error (security measure)
4. **Check Expiration**: Compares the `expires_at` timestamp with current time
   - If expired, deletes the record and returns 401 error
5. **Verify Code**: Uses bcrypt to compare the provided code with the hashed code in database
   - `bcrypt.CompareHashAndPassword(hashed_code_from_db, code_from_user)`
6. **Handle Mismatch**: If comparison fails, returns generic 401 error
7. **Success Flow**:
   - **Delete OTP Record**: Ensures one-time use only
   - **Find/Create User**: Creates a minimal user record for phone-only authentication
   - **Generate Session Token**: Creates JWT with phone and user information
   - **Store Session**: Saves session record in database
   - **Return Token**: Sends access token in 200 OK response

## Security Features

1. **Generic Error Messages**: All verification failures return the same generic message to prevent information leakage
2. **One-Time Use**: OTP is immediately deleted after successful verification
3. **Bcrypt Verification**: Uses secure hash comparison for code validation
4. **Session Management**: Creates proper JWT tokens with expiration
5. **Rate Limiting**: Protected by N8nGuard to prevent brute-force attacks
6. **Automatic Cleanup**: Expired OTPs are deleted during verification attempts

## Database Operations

### OTP Verification
- Queries `auth.otps` collection for valid OTP by phone
- Deletes OTP record after successful verification
- Handles expired OTPs by deletion

### User Management
- Searches for existing users by phone number
- Creates minimal "phone_user" classification if no user exists
- Assigns default "user" role for phone-based authentication

### Session Creation
- Stores session in `auth.sessions` collection
- Links session to user ID and phone number
- Sets 15-minute expiration (configurable via `TOKEN_EXPIRATION_MINUTES`)

## JWT Token Structure

The returned access token contains:

```json
{
  "authorized": true,
  "uid": "user-31991445883",
  "phone": "31991445883",
  "role": "user",
  "roles": ["user"],
  "exp": 1234567890
}
```

## Error Handling

All verification failures return the same generic error message:
- **Code not found**: "Invalid or expired verification code"
- **Code expired**: "Invalid or expired verification code"
- **Code mismatch**: "Invalid or expired verification code"
- **Already verified**: "Invalid or expired verification code"

This prevents attackers from determining the specific failure reason.

## Rate Limiting

**TODO**: Implement additional rate limiting:
- Max 5 verification attempts per phone number per 15 minutes
- Temporary lockout after repeated failures
- Monitoring for suspicious patterns

## Usage Example

```bash
curl -X POST http://localhost:8080/api/v2/auth/verify-otp \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{
    "phone": "31991445883",
    "code": "123456"
  }'
```

## Integration Flow

1. **Generate OTP**: Call `POST /api/v2/auth/generate-otp`
2. **User Receives Code**: Via email (SMS in future)
3. **Verify OTP**: Call `POST /api/v2/auth/verify-otp`
4. **Use Access Token**: Include in `Authorization: Bearer <token>` header for authenticated requests

## Related Endpoints

- `POST /api/v2/auth/generate-otp` - Generate OTP for phone number
- `GET /api/v2/auth/status/:phone` - Check authentication status
- Future: `POST /api/v2/auth/refresh` - Refresh expired tokens

## Future Enhancements

- **SMS Integration**: Replace email with SMS for better UX
- **Enhanced Rate Limiting**: Implement per-phone and per-IP rate limiting
- **Session Management**: Add session invalidation and refresh capabilities
- **User Upgrade**: Allow phone-only users to upgrade to full accounts
- **Audit Logging**: Track verification attempts for security monitoring
