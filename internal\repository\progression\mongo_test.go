package progression

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/config"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// testUserID is a dedicated user ID for data created and cleaned up by this test suite.
const testUserID = "507f1f77bcf86cd799439011"

// MongoRepositoryTestSuite contains the test suite for MongoDB repository operations
type MongoRepositoryTestSuite struct {
	suite.Suite
	db         *mongo.Database
	repository Repository
}

func (suite *MongoRepositoryTestSuite) SetupTest() {
	ctx := context.Background()

	// Initialize environment configuration
	err := config.EnvFromFile("../../../.env")
	if err != nil {
		suite.T().Skipf("Failed to initialize environment configuration: %v - skipping integration tests", err)
		return
	}

	// Get database configuration
	dbURL := os.Getenv("DATABASE_URL")
	dbName := os.Getenv("DATABASE_NAME")

	if dbURL == "" || dbName == "" {
		suite.T().Skip("DATABASE_URL and DATABASE_NAME configuration not set - skipping integration tests")
		return
	}

	// Connect to MongoDB
	clientOptions := options.Client().ApplyURI(dbURL)
	mongoClient, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		suite.T().Skipf("Failed to connect to MongoDB: %v - skipping integration tests", err)
		return
	}

	// Verify MongoDB connection
	err = mongoClient.Ping(ctx, nil)
	if err != nil {
		suite.T().Skipf("Failed to ping MongoDB: %v - skipping integration tests", err)
		return
	}

	suite.db = mongoClient.Database(dbName)
	suite.repository = New(suite.db)
}

func (suite *MongoRepositoryTestSuite) TearDownTest() {
	ctx := context.Background()

	// Clean up only the test data created during the test run
	if suite.db != nil {
		filter := bson.M{"userId": testUserID}

		// Clean events collection
		_, err := suite.db.Collection(repository.PROGRESSIONS_EVENTS).DeleteMany(ctx, filter)
		suite.NoError(err)

		// Clean summaries collection
		_, err = suite.db.Collection(repository.PROGRESSIONS_SUMMARIES).DeleteMany(ctx, filter)
		suite.NoError(err)
	}
}

func TestMongoRepositoryTestSuite(t *testing.T) {
	suite.Run(t, new(MongoRepositoryTestSuite))
}

func (suite *MongoRepositoryTestSuite) TestIndexCreation() {
	ctx := context.Background()

	// Test events collection indexes
	cursor, err := suite.db.Collection(repository.PROGRESSIONS_EVENTS).Indexes().List(ctx)
	suite.Require().NoError(err)
	defer cursor.Close(ctx)

	var indexes []bson.M
	err = cursor.All(ctx, &indexes)
	suite.Require().NoError(err)

	// Check for expected indexes
	expectedIndexes := []string{
		"userId_1_timestamp_1",
		"userId_1_trailId_1_timestamp_1",
		"userId_1_itemId_1_itemType_1",
		"userId_1_action_1_timestamp_1",
	}

	for _, expectedIndex := range expectedIndexes {
		found := false
		for _, index := range indexes {
			if index["name"] == expectedIndex {
				found = true
				break
			}
		}
		suite.True(found, "Expected index %s not found", expectedIndex)
	}
}

func (suite *MongoRepositoryTestSuite) TestCreateEvent() {
	ctx := context.Background()

	// Create a test event
	event := &progression.ProgressEvent{
		UserID:    testUserID,
		TrailID:   "507f1f77bcf86cd799439012",
		ItemID:    "lesson1",
		ContentID: "content1",
		ItemType:  progression.LessonProgressType,
		Action:    progression.StartedActionType,
		Choice: &progression.Choice{
			Identifier: "choice1",
			Next:       "next1",
		},
		Data: map[string]interface{}{
			"score": 100,
		},
		Timestamp: time.Now(),
	}

	err := suite.repository.CreateEvent(ctx, event)
	suite.Require().NoError(err)

	// Verify event was created
	var createdEvent progression.ProgressEvent
	err = suite.db.Collection(repository.PROGRESSIONS_EVENTS).FindOne(ctx, bson.M{"userId": event.UserID}).Decode(&createdEvent)
	suite.Require().NoError(err)
	suite.Equal(event.UserID, createdEvent.UserID)
	suite.Equal(event.TrailID, createdEvent.TrailID)
	suite.Equal(event.ItemID, createdEvent.ItemID)
}

func (suite *MongoRepositoryTestSuite) TestCreateEventsBatch() {
	ctx := context.Background()

	// Create multiple test events
	events := []*progression.ProgressEvent{
		{
			UserID:    testUserID,
			TrailID:   "507f1f77bcf86cd799439012",
			ItemID:    "lesson1",
			ContentID: "content1",
			ItemType:  progression.LessonProgressType,
			Action:    progression.StartedActionType,
			Timestamp: time.Now(),
		},
		{
			UserID:    testUserID,
			TrailID:   "507f1f77bcf86cd799439012",
			ItemID:    "lesson1",
			ContentID: "content2",
			ItemType:  progression.LessonProgressType,
			Action:    progression.CompletedActionType,
			Timestamp: time.Now(),
		},
	}

	err := suite.repository.CreateEventsBatch(ctx, events)
	suite.Require().NoError(err)

	// Verify events were created
	count, err := suite.db.Collection(repository.PROGRESSIONS_EVENTS).CountDocuments(ctx, bson.M{"userId": testUserID})
	suite.Require().NoError(err)
	suite.Equal(int64(2), count)
}

func (suite *MongoRepositoryTestSuite) TestGetUserEvents() {
	ctx := context.Background()

	// Create test events
	events := []*progression.ProgressEvent{
		{
			UserID:    testUserID,
			TrailID:   "507f1f77bcf86cd799439012",
			ItemID:    "lesson1",
			ContentID: "content1",
			ItemType:  progression.LessonProgressType,
			Action:    progression.StartedActionType,
			Timestamp: time.Now().Add(-time.Hour),
		},
		{
			UserID:    testUserID,
			TrailID:   "507f1f77bcf86cd799439012",
			ItemID:    "lesson1",
			ContentID: "content2",
			ItemType:  progression.LessonProgressType,
			Action:    progression.CompletedActionType,
			Timestamp: time.Now(),
		},
	}

	// Insert events directly
	for _, event := range events {
		_, err := suite.db.Collection(repository.PROGRESSIONS_EVENTS).InsertOne(ctx, event)
		suite.Require().NoError(err)
	}

	// Test getting all events
	retrievedEvents, err := suite.repository.GetUserEvents(ctx, testUserID, 0)
	suite.Require().NoError(err)
	suite.Len(retrievedEvents, 2)

	// Test getting limited events
	retrievedEvents, err = suite.repository.GetUserEvents(ctx, testUserID, 1)
	suite.Require().NoError(err)
	suite.Len(retrievedEvents, 1)
}

func (suite *MongoRepositoryTestSuite) TestGetTrailEvents() {
	ctx := context.Background()

	// Create test events for different trails
	events := []*progression.ProgressEvent{
		{
			UserID:    testUserID,
			TrailID:   "trail1",
			ItemID:    "lesson1",
			ContentID: "content1",
			ItemType:  progression.LessonProgressType,
			Action:    progression.StartedActionType,
			Timestamp: time.Now(),
		},
		{
			UserID:    testUserID,
			TrailID:   "trail2",
			ItemID:    "lesson1",
			ContentID: "content1",
			ItemType:  progression.LessonProgressType,
			Action:    progression.StartedActionType,
			Timestamp: time.Now(),
		},
	}

	// Insert events directly
	for _, event := range events {
		_, err := suite.db.Collection(repository.PROGRESSIONS_EVENTS).InsertOne(ctx, event)
		suite.Require().NoError(err)
	}

	// Test getting events for specific trail
	trailEvents, err := suite.repository.GetTrailEvents(ctx, testUserID, "trail1")
	suite.Require().NoError(err)
	suite.Len(trailEvents, 1)
	suite.Equal("trail1", trailEvents[0].TrailID)
}

func (suite *MongoRepositoryTestSuite) TestGetEventsByTimeRange() {
	ctx := context.Background()

	now := time.Now()
	events := []*progression.ProgressEvent{
		{
			UserID:    testUserID,
			TrailID:   "trail1",
			ItemID:    "lesson1",
			ContentID: "content1",
			ItemType:  progression.LessonProgressType,
			Action:    progression.StartedActionType,
			Timestamp: now.Add(-2 * time.Hour),
		},
		{
			UserID:    testUserID,
			TrailID:   "trail1",
			ItemID:    "lesson2",
			ContentID: "content2",
			ItemType:  progression.LessonProgressType,
			Action:    progression.CompletedActionType,
			Timestamp: now.Add(-1 * time.Hour),
		},
		{
			UserID:    testUserID,
			TrailID:   "trail1",
			ItemID:    "lesson3",
			ContentID: "content3",
			ItemType:  progression.LessonProgressType,
			Action:    progression.StartedActionType,
			Timestamp: now.Add(1 * time.Hour),
		},
	}

	// Insert events directly
	for _, event := range events {
		_, err := suite.db.Collection(repository.PROGRESSIONS_EVENTS).InsertOne(ctx, event)
		suite.Require().NoError(err)
	}

	// Test getting events within time range
	start := now.Add(-3 * time.Hour)
	end := now
	timeRangeEvents, err := suite.repository.GetEventsByTimeRange(ctx, testUserID, start, end)
	suite.Require().NoError(err)
	suite.Len(timeRangeEvents, 2) // Should only get the first two events
}

func (suite *MongoRepositoryTestSuite) TestProgressSummaryOperations() {
	ctx := context.Background()

	// Create a test summary
	summary := &progression.ProgressSummary{
		UserID: testUserID,
		Trails: map[string]*progression.TrailSummary{
			"trail1": {
				ID: "trail1",
				LessonsCompleted: map[string]bool{
					"lesson1": true,
					"lesson2": false,
				},
				ChallengeCompleted: false,
				CurrentItem:        "lesson2",
				ProgressPercent:    50,
				IsCompleted:        false,
				TotalRewards:       1,
				LessonsRewarded: map[string]bool{
					"lesson1": true,
				},
				ChallengeRewarded: false,
				LessonProgress: map[string]*progression.LessonProgress{
					"lesson1": {
						Current:   "content2",
						Completed: true,
						Rewarded:  true,
						Path:      []string{"content1", "content2"},
					},
				},
				ChallengeProgress: &progression.ChallengeProgress{
					Current:   "phase2",
					Completed: true,
					Rewarded:  true,
					Phases: map[string]*progression.PhaseProgress{
						"phase1": {
							Current:   "content2",
							Completed: true,
							Path:      []string{"content1", "content2"},
						},
					},
				},
			},
		},
		UpdatedAt: time.Now(),
		Revision:  1,
	}

	// Test saving summary
	err := suite.repository.SaveProgressSummary(ctx, summary)
	suite.Require().NoError(err)

	// Test getting summary
	retrievedSummary, err := suite.repository.GetProgressSummary(ctx, testUserID)
	suite.Require().NoError(err)
	suite.Equal(summary.UserID, retrievedSummary.UserID)
	suite.Equal(len(summary.Trails), len(retrievedSummary.Trails))
	suite.Equal(summary.Trails["trail1"].ID, retrievedSummary.Trails["trail1"].ID)
	suite.Equal(summary.Trails["trail1"].ProgressPercent, retrievedSummary.Trails["trail1"].ProgressPercent)

	// Test invalidating summary
	err = suite.repository.InvalidateProgressSummary(ctx, testUserID)
	suite.Require().NoError(err)

	// Verify summary was deleted
	_, err = suite.repository.GetProgressSummary(ctx, testUserID)
	suite.Error(err) // Should return not found error
}

func (suite *MongoRepositoryTestSuite) TestEventValidation() {
	ctx := context.Background()

	// Test invalid event (missing required fields)
	invalidEvent := &progression.ProgressEvent{
		UserID: testUserID,
		// Missing TrailID, ItemID, etc.
	}

	err := suite.repository.CreateEvent(ctx, invalidEvent)
	suite.Error(err) // Should fail validation

	// Test valid event
	validEvent := &progression.ProgressEvent{
		UserID:    testUserID,
		TrailID:   "507f1f77bcf86cd799439012",
		ItemID:    "lesson1",
		ContentID: "content1",
		ItemType:  progression.LessonProgressType,
		Action:    progression.StartedActionType,
		Timestamp: time.Now(),
	}

	err = suite.repository.CreateEvent(ctx, validEvent)
	suite.NoError(err)
}

func (suite *MongoRepositoryTestSuite) TestEventDefaults() {
	ctx := context.Background()

	// Create event without timestamp and data
	event := &progression.ProgressEvent{
		UserID:    testUserID,
		TrailID:   "507f1f77bcf86cd799439012",
		ItemID:    "lesson1",
		ContentID: "content1",
		ItemType:  progression.LessonProgressType,
		Action:    progression.StartedActionType,
		// Timestamp and Data will be set by SetDefaults()
	}

	err := suite.repository.CreateEvent(ctx, event)
	suite.Require().NoError(err)

	// Verify defaults were set
	var createdEvent progression.ProgressEvent
	err = suite.db.Collection(repository.PROGRESSIONS_EVENTS).FindOne(ctx, bson.M{"userId": event.UserID}).Decode(&createdEvent)
	suite.Require().NoError(err)
	suite.False(createdEvent.Timestamp.IsZero())
	// Note: Data field might be nil in the database even if SetDefaults() sets it
	// because MongoDB might not store empty maps
}
