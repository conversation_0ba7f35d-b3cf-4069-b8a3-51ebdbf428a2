package progression

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
)

type EventReader interface {
	GetUserEvents(ctx context.Context, userID string, limit int) ([]*progression.ProgressEvent, error)
	GetTrailEvents(ctx context.Context, userID, trailID string) ([]*progression.ProgressEvent, error)
	GetEventsByTimeRange(ctx context.Context, userID string, start, end time.Time) ([]*progression.ProgressEvent, error)
}

type EventWriter interface {
	CreateEvent(ctx context.Context, event *progression.ProgressEvent) error
	CreateEventsBatch(ctx context.Context, events []*progression.ProgressEvent) error
}

type SummaryRepository interface {
	GetProgressSummary(ctx context.Context, userID string) (*progression.ProgressSummary, error)
	SaveProgressSummary(ctx context.Context, summary *progression.ProgressSummary) error
	InvalidateProgressSummary(ctx context.Context, userID string) error
}

// New combined interface
type Repository interface {
	EventReader
	EventWriter
	SummaryRepository
}
