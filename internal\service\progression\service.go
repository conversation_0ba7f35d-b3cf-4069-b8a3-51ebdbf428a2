package progression

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/cache"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	_progress "github.com/dsoplabs/dinbora-backend/internal/repository/progression"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/trail"
	"github.com/dsoplabs/dinbora-backend/internal/service/gamification"
	"github.com/dsoplabs/dinbora-backend/internal/service/vault"
	// Added for model.Vault in helpers.go context
)

type Service interface {
	Initialize(ctx context.Context, userId string) error

	// New API
	RecordProgress(ctx context.Context, userId string, body *progression.ProgressionBody) error
	GetUserProgress(ctx context.Context, userId string) (*progression.ProgressSummary, error)
	GetTrailProgress(ctx context.Context, userID, userClassification string, trailID string) (*progression.TrailSummary, error)
	GetUserEvents(ctx context.Context, userID string, limit int) ([]*progression.ProgressEvent, error)

	// Adapted Methods
	FindLesson(ctx context.Context, userId string, userClassification string, trailId string, lessonId string) (*progression.LessonProgress, error)
	FindChallenge(ctx context.Context, userId string, userClassification string, trailId string, challengeId string) (*progression.ChallengeProgress, error)
	FindChallengePhase(ctx context.Context, userId string, userClassification string, trailId string, challengeId string, challengePhase string) (*progression.PhaseProgress, error)
	FindChallengePhaseCurrentPoints(ctx context.Context, userId string, userClassification string, trailId string, challengeId string, phaseId string) (int, error)
	FindTrail(ctx context.Context, userId string, userClassification string, trailId string) (*progression.TrailSummary, error)
	FindByUser(ctx context.Context, userId string) (*progression.Progression, error)

	// Card Services
	FindTrailCards(ctx context.Context, userId string, userClassification string, trailId string) ([]*progression.TrailCard, error)
	FindRegularTrailCards(ctx context.Context, userId string, trailId string) ([]*progression.TrailCard, error)
	FindExtraTrailCards(ctx context.Context, userId string, userClassification string, trailId string) ([]*progression.TrailCard, error)
	FindModuleCards(ctx context.Context, userId string, userClassification string, trailId string, lessonId string) ([]*progression.LessonCard, *progression.ChallengeCard, error)
	FindChallengePhaseCards(ctx context.Context, userId string, userClassification string, trailId string, phaseId string) ([]*progression.ChallengePhaseCard, error)

	// ACL Methods
	CheckUserPermission(ctx context.Context, userClassification string, trailId string) error

	// Legacy Methods to be Removed later on
	LegacyFindTrail(ctx context.Context, userId string, userClassification string, trailId string) (*progression.Trail, error)
	LegacyFindLesson(ctx context.Context, userId string, userClassification string, trailId string, lessonId string) (*progression.Lesson, error)
	LegacyFindChallenge(ctx context.Context, userId string, userClassification string, trailId string, challengeId string) (*progression.Challenge, error)
	LegacyFindChallengePhase(ctx context.Context, userId string, userClassification string, trailId string, challengeId string, challengePhase string) (*progression.ChallengePhase, error)
}

type service struct {

	// New Progression Event API
	Repository          _progress.Repository
	VaultService        vault.Service
	Calculator          *Calculator
	Cache               cache.CacheService   // Correct type: CacheService
	GamificationService gamification.Service // Added gamification service

	// Legacy Stuff
	TrailService trail.Service
}

func New(repository _progress.Repository, trailService trail.Service, vaultService vault.Service, cache cache.CacheService, gamificationService gamification.Service) Service {
	return &service{
		Calculator:          NewCalculator(trailService),
		Repository:          repository,
		TrailService:        trailService,
		VaultService:        vaultService,
		Cache:               cache, // Initialize cache dependency
		GamificationService: gamificationService,
	}
}

func (s *service) RecordProgress(ctx context.Context, userId string, body *progression.ProgressionBody) error {
	event, err := s.createEventFromBody(ctx, userId, body)
	if err != nil {
		return err
	}

	summary, err := s.GetUserProgress(ctx, userId)
	if err != nil {
		return err
	}

	if err := s.Repository.CreateEvent(ctx, event); err != nil {
		return errors.New(errors.Service, "failed to record progression event", errors.Internal, err)
	}

	if err := s.Repository.InvalidateProgressSummary(ctx, userId); err != nil {
		return errors.New(errors.Service, "failed to invalidate progression summary cache", errors.Internal, err)
	}

	cacheKey := fmt.Sprintf("progression:summary:%s", userId)
	if err := s.Cache.Delete(ctx, cacheKey); err != nil {
		return err
	}

	if event.Action == progression.CompletedActionType {
		if err := s.processRewards(ctx, userId, event, summary); err != nil {
			return errors.New(errors.Service, "failed to process rewards for progression event", errors.Internal, err)
		}
	}

	if s.GamificationService != nil {
		if err := s.GamificationService.CheckAchievements(ctx, userId); err != nil {
			// Log error but don't fail the progression update
			log.Printf("Failed to check achievements for user %s: %v", userId, err)
		}
	}

	return nil
}

func (s *service) GetUserProgress(ctx context.Context, userId string) (*progression.ProgressSummary, error) {
	cacheKey := fmt.Sprintf("progression:summary:%s", userId)
	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if summary, ok := cached.(*progression.ProgressSummary); ok {
			return summary, nil
		}
	}

	summary, err := s.Repository.GetProgressSummary(ctx, userId)
	if err == nil && summary != nil {
		// Cache in memory for faster access
		if err = s.Cache.Set(ctx, cacheKey, summary, time.Hour); err != nil {
			return nil, errors.New(errors.Service, "failed to cache user progression summary", errors.Internal, err)
		}
		return summary, nil
	}

	// Calculate from events
	events, err := s.Repository.GetUserEvents(ctx, userId, 0)
	if err != nil {
		return nil, errors.New(errors.Service, "failed to get user progression events", errors.Internal, err)
	}

	summary, err = s.Calculator.CalculateProgressFromEvents(ctx, events, userId)
	if err != nil {
		return nil, errors.New(errors.Service, "failed to calculate user progression summary", errors.Internal, err)
	}

	// Cache the result
	if err := s.Repository.SaveProgressSummary(ctx, summary); err != nil {
		return nil, errors.New(errors.Service, "failed to save user progression summary", errors.Internal, err)
	}

	if err = s.Cache.Set(ctx, cacheKey, summary, time.Hour); err != nil {
		return nil, errors.New(errors.Service, "failed to cache user progression summary", errors.Internal, err)
	}

	return summary, nil
}

func (s *service) GetTrailProgress(ctx context.Context, userID, userClassification string, trailID string) (*progression.TrailSummary, error) {
	if err := s.CheckUserPermission(ctx, userClassification, trailID); err != nil {
		return nil, err
	}

	summary, err := s.GetUserProgress(ctx, userID)
	if err != nil {
		return nil, err
	}

	trailSummary, exists := summary.Trails[trailID]
	if !exists {
		return progression.NewTrailSummary(trailID), nil
	}

	return trailSummary, nil
}

func (s *service) GetUserEvents(ctx context.Context, userID string, limit int) ([]*progression.ProgressEvent, error) {
	return s.Repository.GetUserEvents(ctx, userID, limit)
}

func (s *service) createEventFromBody(ctx context.Context, userID string, body *progression.ProgressionBody) (*progression.ProgressEvent, error) {
	trailContent, err := s.TrailService.Find(ctx, body.Trail)
	if err != nil {
		return nil, err
	}

	return CreateEventFromBody(ctx, userID, body, trailContent)
}

func (s *service) processRewards(ctx context.Context, userID string, event *progression.ProgressEvent, summary *progression.ProgressSummary) error {
	if event.Choice == nil {
		return nil
	}

	if hasCompletedAndRewardedSummary(summary, event) {
		return nil
	}

	var rewardAmount int
	var challengeCompleted bool
	switch event.Choice.Next {
	case string(progression.RewardTypeCoin):
		switch event.ItemType {
		case progression.LessonProgressType:
			rewardAmount = LessonCompletionReward // Lesson completion reward
		case progression.ChallengeProgressType:
			rewardAmount = ChallengeCompletionReward // Challenge completion reward
			challengeCompleted = true
		}
	default:
		return nil
	}

	if challengeCompleted {
		progressSummary, err := s.GetUserProgress(ctx, userID)
		if err != nil {
			return err
		}
		trailSummary, exists := progressSummary.Trails[event.TrailID]
		if !exists {
			return errors.New(errors.Service, "failed to find trail progress summary", errors.Internal, nil)
		}

		if trailSummary.IsCompleted {
			rewardAmount += 1
		}
	}

	if rewardAmount > 0 {
		return s.VaultService.Reward(ctx, userID, rewardAmount)
	}

	return nil
}

//endregion

func (s *service) Initialize(ctx context.Context, userId string) error {
	foundProgress, err := s.FindByUser(ctx, userId)
	if err != nil && err.(*errors.DomainError).Kind() != errors.NotFound {
		return errors.New(errors.Service, "failed to initialize progress service", errors.Internal, err)
	} else if foundProgress != nil {
		return err
	}

	_, err = s.GetUserProgress(ctx, userId)
	return err
}

func (s *service) FindByUser(ctx context.Context, userId string) (*progression.Progression, error) {
	// Get data from new system and convert to legacy format
	summary, err := s.GetUserProgress(ctx, userId)
	if err != nil {
		return nil, err
	}

	return s.convertSummaryToLegacy(ctx, summary)
}

func (s *service) CreateLesson(ctx context.Context, userId string, userClassification string, progressionBody *progression.ProgressionBody) error {
	if err := s.CheckUserPermission(ctx, userClassification, progressionBody.Trail); err != nil {
		return err
	}

	progressionBody.Type = string(progression.LessonProgressType)
	return s.RecordProgress(ctx, userId, progressionBody)
}

func (s *service) FindLesson(ctx context.Context, userId string, userClassification string, trailId string, lessonId string) (*progression.LessonProgress, error) {
	trailSummary, err := s.GetTrailProgress(ctx, userId, userClassification, trailId)
	if err != nil {
		return nil, err
	}

	if trailSummary == nil {
		return nil, nil
	}

	if _, exists := trailSummary.LessonProgress[lessonId]; !exists {
		return nil, nil
	}

	return trailSummary.LessonProgress[lessonId], nil

}

func (s *service) LegacyFindLesson(ctx context.Context, userId string, userClassification string, trailId string, lessonId string) (*progression.Lesson, error) {
	trailSummary, err := s.GetTrailProgress(ctx, userId, userClassification, trailId)
	if err != nil {
		return nil, err
	}

	// Get trail and lesson content for proper availability calculation
	trailContent, err := s.TrailService.Find(ctx, trailId)
	if err != nil {
		return nil, err
	}

	lessonContent := trailContent.GetLesson(lessonId)
	if lessonContent == nil {
		return &progression.Lesson{
			Identifier: lessonId,
			Available:  false,
			Path:       make([]*progression.LessonContent, 0),
		}, nil
	}

	return ConvertLessonProgressToLegacyWithContent(lessonId, trailSummary, lessonContent), nil

}

// Challenge CRUD

func (s *service) CreateChallenge(ctx context.Context, userId string, userClassification string, progressionBody *progression.ProgressionBody) error {
	if err := s.CheckUserPermission(ctx, userClassification, progressionBody.Trail); err != nil {
		return err
	}

	progressionBody.Type = string(progression.ChallengeProgressType)
	return s.RecordProgress(ctx, userId, progressionBody)
}

func (s *service) FindTrail(ctx context.Context, userId string, userClassification string, trailId string) (*progression.TrailSummary, error) {
	trailSummary, err := s.GetTrailProgress(ctx, userId, userClassification, trailId)
	if err != nil {
		return nil, err
	}

	return trailSummary, nil
}

func (s *service) LegacyFindTrail(ctx context.Context, userId string, userClassification string, trailId string) (*progression.Trail, error) {
	trailSummary, err := s.FindTrail(ctx, userId, userClassification, trailId)
	if err != nil {
		return nil, err
	}

	if trailSummary == nil {
		return nil, nil
	}

	trailContent, err := s.TrailService.Find(ctx, trailId)
	if err != nil {
		return nil, err
	}

	return ConvertTrailSummaryToLegacy(trailSummary, trailContent), nil
}

func (s *service) CheckUserPermission(ctx context.Context, userClassification string, trailId string) error {
	trailContent, err := s.TrailService.Find(ctx, trailId)
	if err != nil {
		return err
	}

	if !trailContent.HasAccess(userClassification) {
		return errors.New(errors.Service, "access denied", errors.Forbidden, nil)
	}

	return nil
}

func (s *service) FindChallenge(ctx context.Context, userId string, userClassification string, trailId string, challengeId string) (*progression.ChallengeProgress, error) {
	if err := s.CheckUserPermission(ctx, userClassification, trailId); err != nil {
		return nil, err
	}

	summary, err := s.GetUserProgress(ctx, userId)
	if err != nil {
		return nil, err
	}

	trailSummary, exists := summary.Trails[trailId]
	if !exists {
		return nil, nil
	}

	return trailSummary.ChallengeProgress, nil
}

func (s *service) LegacyFindChallenge(ctx context.Context, userId string, userClassification string, trailId string, challengeId string) (*progression.Challenge, error) {
	if err := s.CheckUserPermission(ctx, userClassification, trailId); err != nil {
		return nil, err
	}

	summary, err := s.GetUserProgress(ctx, userId)
	if err != nil {
		return nil, err
	}

	emptyChallengeProgress := &progression.Challenge{
		Identifier: challengeId,
		Available:  false,
		Completed:  false,
		Rewarded:   false,
		Phases:     make([]*progression.ChallengePhase, 0),
	}

	trailSummary, exists := summary.Trails[trailId]
	if !exists {
		return emptyChallengeProgress, nil
	}

	trailContent, err := s.TrailService.Find(ctx, trailId)
	if err != nil {
		return nil, err
	}

	return ConvertChallengeProgressToLegacyWithContent(challengeId, trailSummary, trailContent), nil
}

func (s *service) LegacyFindChallengePhase(ctx context.Context, userId string, userClassification string, trailId string, challengeId string, challengePhase string) (*progression.ChallengePhase, error) {
	if err := s.CheckUserPermission(ctx, userClassification, trailId); err != nil {
		return nil, err
	}

	challengeProgress, err := s.LegacyFindChallenge(ctx, userId, userClassification, trailId, challengeId)
	if err != nil {
		return nil, err
	}

	for _, phase := range challengeProgress.Phases {
		if phase.Identifier == challengePhase {
			return phase, nil
		}
	}

	return &progression.ChallengePhase{
		Identifier: challengePhase,
		Available:  false,
	}, nil
}

func (s *service) FindChallengePhase(ctx context.Context, userId string, userClassification string, trailId string, challengeId string, challengePhase string) (*progression.PhaseProgress, error) {
	if err := s.CheckUserPermission(ctx, userClassification, trailId); err != nil {
		return nil, err
	}

	challengeProgress, err := s.FindChallenge(ctx, userId, userClassification, trailId, challengeId)
	if err != nil {
		return nil, err
	}

	if challengeProgress == nil {
		return nil, nil
	}

	phaseProgress, exists := challengeProgress.Phases[challengePhase]
	if !exists {
		return nil, nil
	}

	return phaseProgress, nil
}

func (s *service) FindChallengePhaseCurrentPoints(ctx context.Context, userId string, userClassification string, trailId string, challengeId string, phaseId string) (int, error) {
	phaseProgress, err := s.FindChallengePhase(ctx, userId, userClassification, trailId, challengeId, phaseId)
	if err != nil {
		return 0, err
	}

	return phaseProgress.CurrentPoints, nil
}

// Card CRUD
func (s *service) FindTrailCards(ctx context.Context, userId string, userClassification string, trailId string) ([]*progression.TrailCard, error) {
	// Get user progress summary
	summary, err := s.GetUserProgress(ctx, userId)
	if err != nil {
		log.Printf("Error finding trail progressions for user %s: %v", userId, err)
		return nil, err
	}

	// Fetch Card Data
	var fetchedCardDataList []*content.TrailCard
	var trailServiceErr error

	if trailId == "" {
		// Get lightweight card data for all trails
		fetchedCardDataList, trailServiceErr = s.TrailService.FindAllCardData(ctx)
		// Filter for accessible trails
		fetchedCardDataList = filterAccessibleTrails(fetchedCardDataList, userClassification)
	} else {
		// Get lightweight card data for a specific trail
		var singleCardData *content.TrailCard
		singleCardData, trailServiceErr = s.TrailService.FindCardData(ctx, trailId)
		if singleCardData != nil && !singleCardData.HasAccess(userClassification) {
			return nil, errors.New(errors.Service, "access denied", errors.Forbidden, nil)
		}
		if trailServiceErr == nil && singleCardData != nil {
			fetchedCardDataList = []*content.TrailCard{singleCardData}
		}
	}

	// Handle errors from trail service calls
	if trailServiceErr != nil {
		return nil, trailServiceErr
	}

	// Handle cases where no data was found
	if len(fetchedCardDataList) == 0 {
		return nil, nil
	}

	// Create trail cards directly from summary
	cards := make([]*progression.TrailCard, 0, len(fetchedCardDataList))
	for _, cardData := range fetchedCardDataList {
		card := createTrailCardFromSummary(cardData, summary)
		cards = append(cards, card)
	}

	return cards, nil
}

func (s *service) FindRegularTrailCards(ctx context.Context, userId string, trailId string) ([]*progression.TrailCard, error) {
	// Get user progress summary
	summary, err := s.GetUserProgress(ctx, userId)
	if err != nil {
		log.Printf("Error finding trail progressions for user %s: %v", userId, err)
		return nil, err
	}

	// Fetch Card Data
	var fetchedCardDataList []*content.TrailCard
	var trailServiceErr error

	if trailId == "" {
		fetchedCardDataList, trailServiceErr = s.TrailService.FindAllRegularTrailsCardData(ctx)
	} else {
		var singleCardData *content.TrailCard
		singleCardData, trailServiceErr = s.TrailService.FindRegularTrailCardData(ctx, trailId)
		if trailServiceErr == nil && singleCardData != nil {
			fetchedCardDataList = []*content.TrailCard{singleCardData}
		}
	}

	// Handle errors from trail service calls
	if trailServiceErr != nil {
		return nil, trailServiceErr
	}

	// Handle cases where no data was found
	if len(fetchedCardDataList) == 0 {
		return nil, nil
	}

	// Create trail cards directly from summary
	cards := make([]*progression.TrailCard, 0, len(fetchedCardDataList))
	for _, cardData := range fetchedCardDataList {
		card := createTrailCardFromSummary(cardData, summary)
		cards = append(cards, card)
	}

	return cards, nil
}

func (s *service) FindExtraTrailCards(ctx context.Context, userId string, userClassification string, trailId string) ([]*progression.TrailCard, error) {
	// Get user progress summary
	summary, err := s.GetUserProgress(ctx, userId)
	if err != nil {
		log.Printf("Error finding trail progressions for user %s: %v", userId, err)
		return nil, err
	}

	// Fetch Card Data
	var fetchedCardDataList []*content.TrailCard
	var trailServiceErr error

	if trailId == "" {
		fetchedCardDataList, trailServiceErr = s.TrailService.FindAllExtraTrailsCardData(ctx, userClassification)
		// Filter for accessible trails
		fetchedCardDataList = filterAccessibleTrails(fetchedCardDataList, userClassification)
	} else {
		var singleCardData *content.TrailCard
		singleCardData, trailServiceErr = s.TrailService.FindExtraTrailCardData(ctx, trailId, userClassification)
		if singleCardData != nil && !singleCardData.HasAccess(userClassification) {
			return nil, errors.New(errors.Service, "access denied", errors.Forbidden, nil)
		}
		if trailServiceErr == nil && singleCardData != nil {
			fetchedCardDataList = []*content.TrailCard{singleCardData}
		}
	}

	// Handle errors from trail service calls
	if trailServiceErr != nil {
		return nil, trailServiceErr
	}

	// Handle cases where no data was found
	if len(fetchedCardDataList) == 0 {
		return nil, nil
	}

	// Create trail cards directly from summary
	cards := make([]*progression.TrailCard, 0, len(fetchedCardDataList))
	for _, cardData := range fetchedCardDataList {
		card := createTrailCardFromSummary(cardData, summary)
		cards = append(cards, card)
	}

	return cards, nil
}

func (s *service) FindModuleCards(ctx context.Context, userId string, userClassification string, trailId string, lessonId string) ([]*progression.LessonCard, *progression.ChallengeCard, error) {
	// Use the new API directly with the summary
	summary, err := s.GetUserProgress(ctx, userId)
	if err != nil {
		return nil, nil, err
	}

	var contents []*content.Lesson
	var cards []*progression.LessonCard
	var challengeCard *progression.ChallengeCard

	foundContent, err := s.TrailService.Find(ctx, trailId)
	if err != nil {
		return nil, nil, err
	} else if foundContent == nil {
		return nil, nil, nil
	} else if !foundContent.HasAccess(userClassification) {
		return nil, nil, errors.New(errors.Service, "access denied", errors.Forbidden, nil)
	}

	if lessonId == "" {
		contents = foundContent.Lessons
	} else {
		contents = append(contents, foundContent.GetLesson(lessonId))
	}

	// Get trail summary for this specific trail
	trailSummary := summary.Trails[trailId]
	if trailSummary == nil {
		trailSummary = progression.NewTrailSummary(trailId)
	}

	for _, foundLesson := range contents {
		card := createLessonCardFromSummary(foundLesson, trailSummary)
		cards = append(cards, card)
	}

	if foundContent.Challenge != nil {
		challengeCard = createChallengeCardFromSummary(foundContent.Challenge, trailSummary, foundContent)
	}

	return cards, challengeCard, nil
}

func (s *service) FindChallengePhaseCards(ctx context.Context, userId string, userClassification string, trailId string, phaseId string) ([]*progression.ChallengePhaseCard, error) {
	// Get user progress summary
	summary, err := s.GetUserProgress(ctx, userId)
	if err != nil {
		return nil, err
	}

	var contents []*content.ChallengePhase
	var cards []*progression.ChallengePhaseCard

	foundContent, err := s.TrailService.Find(ctx, trailId)
	if err != nil {
		return nil, err
	} else if foundContent == nil || foundContent.Challenge == nil {
		return nil, nil
	} else if !foundContent.HasAccess(userClassification) {
		return nil, errors.New(errors.Service, "access denied", errors.Forbidden, nil)
	}

	if phaseId == "" {
		contents = foundContent.Challenge.Phases
	} else {
		contents = append(contents, foundContent.Challenge.GetPhase(phaseId))
	}

	// Get trail summary for this specific trail
	trailSummary := summary.Trails[trailId]
	if trailSummary == nil {
		trailSummary = progression.NewTrailSummary(trailId)
	}

	for _, foundPhase := range contents {
		card := createChallengePhaseCardFromSummary(foundPhase, trailSummary)
		cards = append(cards, card)
	}

	return cards, nil
}

// Legacy

func (s *service) LegacyCreateLessonChallenge(ctx context.Context, userId string, userClassification string, progressionBody *progression.ProgressionBody) error {
	if err := s.CheckUserPermission(ctx, userClassification, progressionBody.Trail); err != nil {
		return err
	}

	return s.RecordProgress(ctx, userId, progressionBody)
}
