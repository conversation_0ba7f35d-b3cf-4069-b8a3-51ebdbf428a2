package financialsheet

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// TestCreateTransaction_TimezoneHandling tests that transactions preserve the correct local date
// regardless of the time of day and timezone conversion
func TestCreateTransaction_TimezoneHandling(t *testing.T) {
	tests := []struct {
		name               string
		inputDateUTC       time.Time // Date as it would come from JSON (already converted to UTC)
		userTimezone       string
		expectedStoredDate time.Time // Expected date to be stored (start of day in UTC)
		description        string
	}{
		{
			name:               "Late night UTC-3 should preserve local date",
			inputDateUTC:       time.Date(2025, 9, 17, 0, 0, 0, 0, time.UTC), // 00:00 UTC = 21:00 UTC-3 previous day
			userTimezone:       "America/Sao_Paulo",                          // UTC-3
			expectedStoredDate: time.Date(2025, 9, 16, 0, 0, 0, 0, time.UTC), // Should store Sept 16, not 17
			description:        "User created transaction at 21:00 local time (Sept 16), which becomes 00:00 UTC (Sept 17)",
		},
		{
			name:               "Early morning UTC-3 should preserve local date",
			inputDateUTC:       time.Date(2025, 9, 16, 6, 0, 0, 0, time.UTC), // 06:00 UTC = 03:00 UTC-3 same day
			userTimezone:       "America/Sao_Paulo",                          // UTC-3
			expectedStoredDate: time.Date(2025, 9, 16, 0, 0, 0, 0, time.UTC), // Should store Sept 16
			description:        "User created transaction at 03:00 local time (Sept 16), which becomes 06:00 UTC (Sept 16)",
		},
		{
			name:               "Late night UTC+9 should preserve local date",
			inputDateUTC:       time.Date(2025, 9, 15, 15, 0, 0, 0, time.UTC), // 15:00 UTC = 00:00 UTC+9 next day
			userTimezone:       "Asia/Tokyo",                                  // UTC+9
			expectedStoredDate: time.Date(2025, 9, 16, 0, 0, 0, 0, time.UTC),  // Should store Sept 16, not 15
			description:        "User created transaction at 00:00 local time (Sept 16), which becomes 15:00 UTC (Sept 15)",
		},
		{
			name:               "UTC timezone should work correctly",
			inputDateUTC:       time.Date(2025, 9, 16, 12, 30, 0, 0, time.UTC), // 12:30 UTC
			userTimezone:       "UTC",
			expectedStoredDate: time.Date(2025, 9, 16, 0, 0, 0, 0, time.UTC), // Should store Sept 16
			description:        "User in UTC timezone created transaction at 12:30 UTC (Sept 16)",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Arrange
			ctx := context.Background()
			mockRepo := &MockRepository{}
			mockLeague := &MockLeagueService{}

			service := &service{
				Repository:    mockRepo,
				LeagueService: mockLeague,
			}

			// Load user timezone
			userTimezone, err := time.LoadLocation(tt.userTimezone)
			assert.NoError(t, err, "Should be able to load timezone %s", tt.userTimezone)

			// Create test record
			record := &financialsheet.Record{
				ObjectID:           primitive.NewObjectID(),
				UserID:             "user123",
				UserName:           "Test User",
				Points:             financialsheet.Points{Current: 0, Best: 0},
				YearData:           make(map[int]financialsheet.YearData),
				Balance:            monetary.Amount(0),
				TotalIncome:        monetary.Amount(0),
				TotalCostsOfLiving: monetary.Amount(0),
				TotalExpenses:      monetary.Amount(0),
			}

			// Create transaction with the input date (simulating JSON parsing)
			transaction := &financialsheet.Transaction{
				Category:      "food",
				MoneySource:   financialsheet.MoneySourceOpt1,
				Value:         monetary.Amount(5000),
				Date:          tt.inputDateUTC, // This simulates the date as it comes from JSON
				PaymentMethod: financialsheet.PaymentMethodOpt1,
				Type:          financialsheet.CategoryTypeExpense,
			}

			// Mock category lookup
			category := &financialsheet.Category{
				ID:         primitive.NewObjectID().Hex(),
				Identifier: "food",
				Name:       "Food",
				Type:       financialsheet.CategoryTypeExpense,
				User:       "user123",
			}

			// Mock expectations
			mockRepo.On("FindCategoryByUserAndIdentifier", ctx, "user123", financialsheet.CategoryIdentifier("food")).Return(category, nil)
			mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
			mockLeague.On("RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time")).Return(nil)

			// Debug: Print the input and expected dates
			t.Logf("Input date (UTC): %v", tt.inputDateUTC)
			t.Logf("User timezone: %s", tt.userTimezone)
			t.Logf("Expected stored date: %v", tt.expectedStoredDate)

			// Debug: Manual timezone conversion
			localTime := tt.inputDateUTC.In(userTimezone)
			t.Logf("Local time: %v", localTime)
			expectedDate := time.Date(localTime.Year(), localTime.Month(), localTime.Day(), 0, 0, 0, 0, time.UTC)
			t.Logf("Manual calculation: %v", expectedDate)

			// Act
			updatedRecord, err := service.CreateTransaction(ctx, record, transaction, false, userTimezone)

			// Assert
			assert.NoError(t, err, "CreateTransaction should succeed")
			assert.NotNil(t, updatedRecord, "Updated record should not be nil")

			// Debug: Print actual result
			t.Logf("Actual stored date: %v", transaction.Date)

			// Verify the transaction date was stored correctly
			assert.True(t, transaction.Date.Equal(tt.expectedStoredDate),
				"Transaction date should be %v but got %v. %s",
				tt.expectedStoredDate, transaction.Date, tt.description)

			// Verify the date is stored as start of day (00:00:00) in UTC
			assert.Equal(t, 0, transaction.Date.Hour(), "Stored date should have hour 00")
			assert.Equal(t, 0, transaction.Date.Minute(), "Stored date should have minute 00")
			assert.Equal(t, 0, transaction.Date.Second(), "Stored date should have second 00")
			assert.Equal(t, time.UTC, transaction.Date.Location(), "Stored date should be in UTC")

			mockRepo.AssertExpectations(t)
			mockLeague.AssertExpectations(t)
		})
	}
}

// TestUpdateTransaction_TimezoneHandling tests that transaction updates preserve the correct local date
func TestUpdateTransaction_TimezoneHandling(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}

	service := &service{
		Repository: mockRepo,
	}

	// Load user timezone (UTC-3)
	userTimezone, err := time.LoadLocation("America/Sao_Paulo")
	assert.NoError(t, err)

	// Create test record with existing transaction
	existingTransactionID := primitive.NewObjectID()
	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		YearData: map[int]financialsheet.YearData{
			2025: {
				"09": financialsheet.MonthData{
					Transactions: []financialsheet.Transaction{
						{
							ObjectID:      existingTransactionID,
							Category:      "food",
							MoneySource:   financialsheet.MoneySourceOpt1,
							Value:         monetary.Amount(3000),
							Date:          time.Date(2025, 9, 15, 0, 0, 0, 0, time.UTC),
							PaymentMethod: financialsheet.PaymentMethodOpt1,
							Type:          financialsheet.CategoryTypeExpense,
						},
					},
					Categories: []financialsheet.CategoryCard{
						{
							Identifier: "food",
							Name:       "Food",
							Value:      monetary.Amount(3000),
						},
					},
				},
			},
		},
		Balance:            monetary.Amount(-3000),
		TotalIncome:        monetary.Amount(0),
		TotalCostsOfLiving: monetary.Amount(0),
		TotalExpenses:      monetary.Amount(3000),
	}

	// Create updated transaction with new date (simulating JSON parsing)
	// This represents 21:00 local time (Sept 16) which becomes 00:00 UTC (Sept 17)
	updatedTransaction := &financialsheet.Transaction{
		ID:            existingTransactionID.Hex(),
		Category:      "food",
		MoneySource:   financialsheet.MoneySourceOpt1,
		Value:         monetary.Amount(5000),                        // Changed value
		Date:          time.Date(2025, 9, 17, 0, 0, 0, 0, time.UTC), // This simulates the date as it comes from JSON
		PaymentMethod: financialsheet.PaymentMethodOpt1,
		Type:          financialsheet.CategoryTypeExpense,
	}

	// Mock category lookup
	category := &financialsheet.Category{
		ID:         primitive.NewObjectID().Hex(),
		Identifier: "food",
		Name:       "Food",
		Type:       financialsheet.CategoryTypeExpense,
		User:       "user123",
	}

	// Mock expectations
	mockRepo.On("FindCategoryByUserAndIdentifier", ctx, "user123", financialsheet.CategoryIdentifier("food")).Return(category, nil)
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)

	// Act
	updatedRecord, err := service.UpdateTransaction(ctx, record, updatedTransaction, userTimezone)

	// Assert
	assert.NoError(t, err, "UpdateTransaction should succeed")
	assert.NotNil(t, updatedRecord, "Updated record should not be nil")

	// Verify the transaction date was stored correctly (should be Sept 16, not 17)
	expectedStoredDate := time.Date(2025, 9, 16, 0, 0, 0, 0, time.UTC)
	assert.True(t, updatedTransaction.Date.Equal(expectedStoredDate),
		"Transaction date should be %v but got %v. User created transaction at 21:00 local time (Sept 16), which becomes 00:00 UTC (Sept 17), but should be stored as Sept 16",
		expectedStoredDate, updatedTransaction.Date)

	mockRepo.AssertExpectations(t)
}
