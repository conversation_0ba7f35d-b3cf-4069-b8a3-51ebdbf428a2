{"apple": {"error": {"accessTokenRequired": "Oops! Your session has expired. Please log in again.", "invalidOboardingAgeRange": "Hmm, that age looks a bit odd. How about trying again?", "invalidOboardingFinancialSituation": "Looks like that's not a valid option. Please choose one from the list.", "invalidOboardingFinancialGoal": "Looks like that goal isn't a valid option. Please choose one from the list.", "invalidOboardingPersonalInterest": "Looks like that interest isn't a valid option. Please choose one from the list.", "failedToProcessPhoto": "Oops! Something went wrong with your photo. Shall we try another one?", "invalidFileType": "Hmm, this file type won't work. Please try a JPG, JPEG, PNG or HEIC photo.", "fileTooLarge": "That photo is a bit heavy! Please try a smaller image (up to 5MB).", "invalidLoginInput": "Oops! It looks like a field is empty. Please check your details and try again.", "": "", "fetchFailed": "Oops! We couldn't fetch your Apple information. Please try again.", "decodeFailed": "Oops! We had a little trouble processing your Apple information. Please try again.", "invalidToken": "Oops! Your access token has expired. Please log in again.", "missingKid": "Oops! We couldn't find the security key. Please try again.", "publicKeyParseFailed": "Oops! We had a little trouble processing the security key. Please try again."}}, "auth": {"error": {"invalidLoginInput": "Oops! It looks like a field is empty. Please check your details and try again.", "loginValidationFailed": "Hmm, that email or password doesn't look right. Want to give it another try?", "invalidRegisterInput": "Oops! It looks like a field is empty. Please fill in all the details to continue.", "failedToParseFormData": "Uh-oh! We had a little trouble reading your info. Could you please try submitting it again?", "invalidOboardingAgeRange": "Hmm, something seems off with the age range. Please double-check and try again.", "invalidOboardingFinancialSituation": "Oops, something about your financial situation seems incorrect. Could you please check it?", "invalidOboardingFinancialGoal": "Hmm, we didn't quite get your financial goal. How about reviewing and trying again?", "invalidOboardingPersonalInterest": "It seems your interests weren't saved. Let's give it one more try!", "failedToProcessPhoto": "We couldn't upload your photo. Maybe try a different one or give it another go in a moment?", "invalidFileType": "That file type isn't supported. Please use a JPG, JPEG, PNG or HEIC image!", "fileTooLarge": "Whoa, that's a big photo! Please try one that's 5MB or smaller.", "invalidRefreshTokenInput": "Oops, something unexpected happened on our end. Please try that again.", "invalidForgotPasswordInput": "Hmm, something went wrong. Check your details and try requesting a recovery link again.", "invalidResetPasswordInput": "Oops! Something went wrong while resetting your password. Let's try one more time.", "invalidCheckPasswordInput": "That password doesn't look quite right. Please take a look and try again.", "userNotLoggedIn": "It looks like you're not logged in. Please sign in to continue your adventure!", "invalidAdminLoginInput": "Hmm, the admin login details don't seem right. Please check them and try again.", "": "", "failedToUploadPhoto": "Oops, we couldn't upload your photo. How about trying with a different image?", "failedToCreateToken": "Oops, something went wrong on our end. Please try again in a moment.", "failedToRetrieveUserAfterCreation": "Your account was created, but we couldn't connect you right now. How about trying to log in?", "brevoNotifierNotAvailable": "Oops! We couldn't send the email right now. Please try again in a few minutes.", "userNotAdmin": "Oops! It looks like this is a restricted area. Just for the game masters!", "userNotHR": "Oops! It looks like this is a restricted area. Just for the HR!", "invalidHRLoginInput": "Hmm, the HR login details don't seem right. Please check them and try again.", "notFoundByPhone": "No user found with this phone number. Please register a phone number in your profile."}}, "financialplan": {"error": {"unauthorized": "Oops! You don't have permission to access this resource.", "invalidPeriod": "Invalid period. Please check the data and try again.", "invalidCategoryType": "Invalid category type. Please check the data and try again."}}, "financialsheet": {"error": {"conflict": "You already have a financial sheet! Keep organizing your finances.", "createFailed": "Oops! We couldn't create your financial sheet. Please try again.", "findFailed": "Oops! We couldn't load your financial sheet. Please try again.", "findAllFailed": "Oops! We couldn't load the financial sheets. Please try again.", "notFound": "Financial sheet not found. How about creating a new one?", "invalidId": "Invalid financial sheet ID. Please check your data and try again.", "userNotFound": "Financial sheet not found for this user. Start by creating one!", "findByUserFailed": "Oops! We couldn't load your financial sheet. Please try again.", "findByUsersFailed": "Oops! We couldn't load the financial sheets. Please try again.", "conflictUpdate": "Couldn't update your financial sheet. Please try again.", "updateFailed": "Oops! We couldn't save your changes. Please try again.", "deleteFailed": "Oops! We couldn't delete your financial sheet. Please try again.", "invalidMonth": "Oops! The month must be between 1 and 12. Please check and try again.", "duplicateMonth": "Whoops! You've already chosen this month. Select different months for recurrence.", "sameMonthAsOriginal": "Can't repeat in the same month as the original transaction. Choose other months!", "recordAlreadyExists": "You already have a financial sheet! Keep organizing your finances.", "invalidRecord": "Oops! Invalid data. Please check your information and try again.", "noTransactionsAlreadyMarked": "You've already marked 'no transactions' for today! Continue your streak tomorrow.", "noTransactionsAlreadyMarkedDate": "You've already marked 'no transactions' for this date. Choose another day!", "cannotMarkSameDayAsTransaction": "Can't mark 'no transactions' on the same day as a real transaction.", "invalidFinancialSheetId": "Invalid financial sheet ID. Please check the data and try again.", "invalidMonthParameter": "Invalid month! Must be between 1 and 12. Please check and try again.", "invalidYearParameter": "Invalid year! Please check the format and try again.", "invalidInput": "Oops! Invalid data. Please check the information and try again.", "validationFailed": "Some fields aren't filled correctly. Please take a look!", "dreamTransactionInput": "Oops! We couldn't process your dream data. Please try again.", "dreamTransactionValidation": "Some of your dream data is incorrect. Please check and try again.", "invalidTransactionId": "Invalid transaction ID. Please check the data and try again.", "invalidCategoryIdParam": "Oops! Invalid category ID in the URL parameter. Please check the URL and try again.", "invalidMoneySource": "Oops! Invalid money source. Please choose a valid option and try again.", "invalidPaymentMethod": "Oops! Invalid payment method. Please select a valid payment option.", "invalidCategoryIdentifier": "Invalid category identifier. Please check the data and try again.", "invalidCategoryType": "Invalid category type. Please choose a valid type and try again.", "invalidCategoryBackground": "Invalid category background color. Please select a valid color.", "invalidFinancialRecordId": "Oops! Invalid financial record ID. Please check the data and try again.", "invalidCategoryId": "Oops! Invalid category ID. Please check the data and try again.", "cannotDeleteSystemCategories": "Whoops! You can't delete system categories. They're essential for the app to work!", "canOnlyDeleteOwnCategories": "You can only delete your own categories. This one isn't yours!", "categoryInUse": "This category is being used in transactions! Remove the transactions first.", "transactionTypeMismatch": "Oops! The transaction type doesn't match the category type. Please check and try again.", "invalidTransaction": "Oops! Invalid transaction data. Please check the information and try again.", "invalidDreamId": "Oops! Invalid dream ID. Please check the data and try again.", "transactionNotFound": "Transaction not found. It may have been removed or doesn't exist.", "cannotMarkBeforeYesterday": "Can't mark 'no transactions' for dates before yesterday. Stay focused on the present!"}, "category": {"error": {"conflict": "This category already exists! Choose a different name.", "createFailed": "Oops! We couldn't create your category. Please try again.", "findFailed": "Oops! We couldn't load your categories. Please try again.", "notFound": "Category not found. How about creating a new one?", "invalidId": "Invalid category ID. Please check your data and try again.", "conflictUpdate": "Couldn't update your category. Please try again.", "updateFailed": "Oops! We couldn't save the category changes. Please try again.", "deleteFailed": "Oops! We couldn't delete your category. Please try again.", "findByIdFailed": "Oops! We couldn't find this category. Please try again.", "findByNameFailed": "Oops! We couldn't find the category by name. Please try again."}}}, "progression": {"error": {"conflict": "You already have saved progress! Continue where you left off.", "createFailed": "Oops! We couldn't save your progress. Please try again.", "invalidIdFormat": "Invalid progress ID. Please check the data and try again.", "invalidEvent": "Invalid progression event. Please check the data and try again.", "notFound": "Progress not found. How about starting a new journey?", "findFailed": "Oops! We couldn't load your progress. Please try again.", "notFoundForUser": "No progress found. Start your learning journey now!", "findByUserFailed": "Oops! We couldn't load your progress. Please try again.", "updateConflict": "Unable to update your progress. Please try again.", "updateFailed": "Oops! We couldn't save your achievements. Please try again.", "notFoundForUpdate": "Progress not found for update. Please try again.", "deleteFailed": "Oops! We couldn't reset your progress. Please try again.", "notFoundForDeletion": "Progress not found for deletion. It may have already been removed.", "findTrailProgressionsFailed": "Oops! We couldn't load your trails. Please try again."}}, "user": {"error": {"hashPassword": "Oops! We had a little trouble saving your password. How about trying again?", "forbidden": "Whoops! It looks like you need a special key to access this.", "invalidCredentials": "Hmm, that email and password combo doesn't look right. Let's give it another try!", "resetPassword": "Oops! Something went wrong while trying to reset your password. Please give it one more try.", "mergeFailed": "Uh-oh, we had a problem updating your information. Could you please try again?", "processPassword": "Oops, we had a little trouble with your password. Could you please try again?", "emailRequired": "Oops, the email is missing! Please fill it in to continue.", "invalidEmail": "Hmm, that email doesn't look quite right. How about taking a look?", "emptyId": "Oops! Something unexpected happened on our end. Please try again.", "nameRequired": "Almost there! We just need your name to continue.", "passwordRequired": "Don't forget your password! It's super important for keeping your account safe.", "referralCodeRequired": "The referral code is missing! Please fill it in to continue.", "setRoleNotAllowed": "Whoops! That's a super-powerful action that can't be done from here.", "phoneRequired": "Oops, we need your phone number to continue.", "passwordRequirements": "For a super-secure password, it needs:\n- At least 6 characters\n- One uppercase letter (A-Z)\n- One lowercase letter (a-z)\n- One number (0-9)\n- One special character (!@#$)", "invalidPhoneNumber": "Ops! Invalid phone number. Please check and try again.", "": "", "conflict": "This user already exists. Try with a different email!", "notFoundById": "User not found. Please check if the ID is correct.", "notFoundByEmail": "No user found with this email. How about creating an account?", "notFoundByPhone": "No user found with this phone number. Please register a phone number in your profile.", "findByPhoneFailed": "Ops! Something went wrong while searching for the user. Please try again.", "notFoundByReferral": "Invalid referral code. Please check if you typed it correctly.", "deletedNotFoundByEmail": "No deleted account found with this email.", "conflictUpdate": "Unable to update. This email is already being used by another user.", "notFoundForUpdate": "User not found for update. Please try again.", "notFoundForDeletion": "User not found for deletion. It may have already been removed.", "createFailed": "Oops! Something went wrong while creating your account. Please try again.", "deletedCreateFailed": "Oops! Something went wrong. Please try again later.", "findByIdFailed": "Oops! Something went wrong while searching for the user. Please try again.", "findAllFailed": "Oops! Something went wrong while loading users. Please try again.", "decodeUserFailed": "Oops! Something went wrong. Please try again later.", "adminUsersNotFound": "Oops! Something went wrong while searching for administrators. Please try again.", "accessDenied": "Oops! Access denied. You don't have permission to perform this action.", "decodeAdminUserFailed": "Oops! Something went wrong. Please try again later.", "findByEmailFailed": "Oops! Something went wrong while searching for the user. Please try again.", "findByReferralFailed": "Oops! Something went wrong while verifying the code. Please try again.", "findByReferringUserIdFailed": "Oops! Something went wrong. Please try again later.", "cursorError": "Oops! Something went wrong. Please try again later.", "findWithFilterFailed": "Oops! Something went wrong while searching for users. Please try again.", "deletedFindByEmailFailed": "Oops! Something went wrong. Please try again later.", "invalidId": "Invalid user ID. Please check the data and try again.", "updateFailed": "Oops! Something went wrong while updating. Please try again.", "deleteFailed": "Oops! Something went wrong while deleting. Please try again.", "deletedConflictExists": "This account has already been deleted previously."}}, "dreamboard": {"error": {"conflict": "You already have a dream board! Keep organizing your goals.", "createFailed": "Oops! We couldn't create your dream board. Please try again.", "findFailed": "Oops! We couldn't load your dream board. Please try again.", "notFound": "Dream board not found. How about creating a new one?", "invalidId": "Invalid dream board ID. Please check your data and try again.", "findAllFailed": "Oops! We couldn't load the dream boards. Please try again.", "decodeFailed": "Oops! Something went wrong processing the data. Please try again.", "findByUserFailed": "Oops! We couldn't find your dream board. Please try again.", "conflictUpdate": "Couldn't update your dream board. Please try again.", "updateFailed": "Oops! We couldn't save your changes. Please try again.", "deleteFailed": "Oops! We couldn't delete your dream board. Please try again.", "deleteCreateFailed": "Oops! We couldn't move to trash. Please try again.", "deletedNotFound": "Deleted dream board not found. It may have already been removed.", "deletedFindFailed": "Oops! We couldn't find it in trash. Please try again.", "dreamAddFailed": "Oops! We couldn't add your dream. Please try again.", "dreamUpdateFailed": "Oops! We couldn't update your dream. Please try again.", "dreamNotFound": "Dream not found. It may have been removed or doesn't exist.", "dreamRemoveFailed": "Oops! We couldn't remove the dream. Please try again.", "categoryAddFailed": "Oops! We couldn't add the category. Please try again.", "categoryNotFound": "Category not found. It may have been removed or doesn't exist.", "categoryFindFailed": "Oops! We couldn't find the category. Please try again.", "categoryUpdateFailed": "Oops! We couldn't update the category. Please try again.", "categoryDeleteFailed": "Oops! We couldn't delete the category. Please try again.", "categoryCreateFailed": "Oops! We couldn't create the category. Please try again.", "sessionStartFailed": "Oops! We couldn't start the operation. Please try again.", "transactionFailed": "Oops! We couldn't complete the operation. Please try again.", "shareLinkConflict": "This share link already exists. Try with a different code.", "shareLinkCreateFailed": "Oops! We couldn't create the share link. Please try again.", "shareLinkNotFound": "Share link not found. It may have expired or doesn't exist.", "shareLinkFindFailed": "Oops! We couldn't find the link. Please try again.", "shareLinkUpdateFailed": "Oops! We couldn't update the link. Please try again.", "shareLinkDeleteFailed": "Oops! We couldn't delete the link. Please try again.", "contributionCreateFailed": "Oops! We couldn't register your contribution. Please try again.", "contributionNotFound": "Contribution not found. It may have been removed or doesn't exist.", "contributionFindFailed": "Oops! We couldn't find the contributions. Please try again.", "contributionDecodeFailed": "Oops! Something went wrong processing the contributions. Please try again.", "contributionCursorError": "Oops! Something went wrong loading the contributions. Please try again.", "contributionUpdateFailed": "Oops! We couldn't update the contribution. Please try again.", "contributionStatusUpdateFailed": "Oops! We couldn't update the contribution status. Please try again.", "contributionDeleteFailed": "Oops! We couldn't delete the contribution. Please try again.", "categoryIdentifierEmpty": "Category identifier is required. How about choosing a unique name?", "categoryNameEmpty": "Category name is required. Give it a special name!", "categoryIconEmpty": "Category icon is required. Choose an icon that represents it well!", "categoryColorEmpty": "Category color is required. Pick a color you like!", "categoryIdentifierInvalid": "Identifier must contain only lowercase letters and numbers. Try again!", "categoryColorInvalid": "Color must be in hexadecimal format (e.g., #FF6347). Please check and try again!", "categoryUnmarshalFailed": "Oops! We couldn't process the category data. Please try again.", "dreamCategoryInvalid": "Dream category is invalid. Please choose a valid category!", "dreamTitleInvalid": "Title must be between 1 and 100 characters. How about a more creative title?", "dreamTimeFrameInvalid": "Selected timeframe is invalid. Choose between short, medium, or long term!", "dreamDeadlineInvalid": "Deadline must be at least 24 hours in the future. Choose a later date!", "dreamCostNegative": "Estimated cost cannot be negative. Please enter a valid amount!", "dreamSavingsInvalid": "Monthly savings must be between 0 and total cost. Please adjust the amount!", "dreamMoneySourceInvalid": "Selected money source is invalid. Please choose a valid option!", "dreamCreatorRequired": "Dream creator must be specified for shared dreams.", "dreamFundingStatusInvalid": "Funding status is invalid. Please check the data!", "dreamRaisedAmountNegative": "Raised amount cannot be negative. Please enter a valid amount!", "dreamboardUserRequired": "Dream board user must be specified.", "dreamboardDreamsInvalid": "Dreams list is invalid. Please check the data!", "dreamboardCategoriesInvalid": "Categories list is invalid. Please check the data!", "dreamboardDatesInvalid": "Creation date cannot be after update date.", "contributionDreamIdRequired": "Dream ID is required for contributions.", "contributionUserIdRequired": "Contributor user ID is required.", "contributionAmountNegative": "Monthly contribution amount cannot be negative.", "contributionStatusInvalid": "Contribution status is invalid. Please check the data!", "shareLinkDreamIdRequired": "Dream ID is required for share links.", "shareLinkCodeRequired": "Link code must be specified.", "shareLinkExpired": "Expiration date must be in the future.", "invalidInput": "Oops! The submitted data is invalid. Please check and try again.", "categoryExists": "This category already exists! How about choosing a different name?", "categoryInUse": "This category is being used by one or more dreams. Remove the dreams first!", "categoryNotInDreamboard": "This category doesn't exist in your dream board.", "alreadyExists": "You already have a dream board! Keep organizing your goals.", "tokenGenerationFailed": "Oops! We couldn't generate the invite code. Please try again.", "inviteLinkDisabled": "This invite link is disabled. Please request a new link!", "inviteLinkExpired": "This invite link has expired. Please request a new link!", "userAlreadyContributing": "You're already contributing to this dream! Keep it up!", "missingParam": "Required parameter not found. Please check the data!", "invalidType": "Invalid parameter type. Must be 'personal' or 'shared'.", "dreamIdRequired": "Dream ID is required. Please check the data!", "contributionIdRequired": "Contribution ID is required. Please check the data!", "codeRequired": "Code is required. Please check the data!", "unauthorized": "You can only manage your own dreams and categories.", "accessDenied": "Contribution not found or access denied.", "validationRequired": "Code and monthly contribution amount are required."}}, "google": {"error": {"accessTokenRequired": "Oops! Your session has expired. Please log in again.", "InvalidOboardingAgeRange ": "Hmm, that age looks a bit odd. How about trying again?", "InvalidOboardingFinancialSituation": "Looks like that's not a valid option. Please choose one from the list.", "InvalidOboardingFinancialGoal": "Looks like that goal isn't a valid option. Please choose one from the list.", "InvalidOboardingPersonalInterest": "Looks like that interest isn't a valid option. Please choose one from the list.", "FailedToProcessPhoto": "Oops! Something went wrong with your photo. Shall we try another one?", "InvalidFileType": "Hmm, this file type won't work. Please try a JPG, JPEG, PNG or HEIC photo.", "FileTooLarge": "That photo is a bit heavy! Please try a smaller image (up to 5MB).", "InvalidToken": "Oops! Your session has expired. Please log in again to continue.", "": "", "fetchFailed": "Oops! We couldn't connect. Please check your internet and try again.", "decodeFailed": "Whoops! We had a little trouble processing your data. Please try again in a moment."}}}