package auth

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"log"
	"math/big"
	"mime/multipart"
	"net/url"
	"os"
	"strconv"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/auth"
	"github.com/dsoplabs/dinbora-backend/internal/repository/auth/otp"
	"github.com/dsoplabs/dinbora-backend/internal/repository/auth/session"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing"
	"github.com/dsoplabs/dinbora-backend/internal/service/notification"
	"github.com/dsoplabs/dinbora-backend/internal/service/s3"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/nyaruka/phonenumbers"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"golang.org/x/crypto/bcrypt"
)

type Service interface {
	// User Auth
	Register(context.Context, *model.User, *multipart.FileHeader, string) (*token.Token, error)
	LegacyRegister(context.Context, *model.User, string) (*token.Token, error)
	Login(context.Context, *model.User) (*model.User, *token.Token, error)
	RefreshAuth(context.Context, string) (*model.User, *token.Token, error)
	ForgotPassword(context.Context, string) error
	ResetPassword(context.Context, string, string) error
	CheckPassword(context.Context, string, string) error

	// Admin Auth
	AdminLogin(context.Context, *model.User) (*token.Token, error)

	// HR Auth
	HRLogin(context.Context, *model.User) (*token.Token, error)

	// Session and OTP Auth
	GetStatusByPhone(context.Context, string) (string, string, error)
	GenerateOTP(context.Context, string) error
	VerifyOTP(context.Context, string, string) (string, error)
}

type service struct {
	UserService         user.Service
	BillingService      billing.Service
	NotificationService *notification.Service
	S3Service           s3.Service
	SessionRepository   session.Repository
	OTPRepository       otp.Repository
}

func New(userService user.Service, billingService billing.Service, notificationService *notification.Service, s3Service s3.Service, sessionRepo session.Repository, otpRepo otp.Repository) Service {
	return &service{
		UserService:         userService,
		BillingService:      billingService,
		NotificationService: notificationService,
		S3Service:           s3Service,
		SessionRepository:   sessionRepo,
		OTPRepository:       otpRepo,
	}
}

// User Auth
func (s *service) Register(ctx context.Context, user *model.User, photo *multipart.FileHeader, referralCode string) (*token.Token, error) {
	if photo != nil {
		photoURL, err := s.S3Service.UploadFile(ctx, photo, os.Getenv("AWS_S3_USER_PHOTOS_FOLDER"))
		if err != nil {
			return nil, errors.NewInternalError(errors.Service, "failed to upload photo", errors.KeyAuthErrorFailedToUploadPhoto, err)
		}
		user.PhotoURL = photoURL
	}

	if err := user.PrepareCreate(); err != nil {
		return nil, err
	}

	if err := s.UserService.Create(ctx, user, referralCode); err != nil {
		return nil, err
	}

	createdUser, err := s.UserService.FindByEmail(ctx, user.Email)
	if err != nil {
		// Attempt to find by ID if email search fails or if email is not unique before creation
		// This part might need adjustment based on how UserService.Create handles user ID generation
		// For now, we assume FindByEmail is the primary way post-creation.
		return nil, errors.NewNotFoundError(errors.Service, "failed to retrieve user after creation", errors.KeyAuthErrorFailedToRetrieveUserAfterCreation, err)
	}

	// Link any existing subscriptions to the newly created user
	userObjectID, err := primitive.ObjectIDFromHex(createdUser.ID)
	if err == nil {
		// Don't fail registration if subscription linking fails, just log it
		if err := s.BillingService.LinkExistingSubscriptionsToUser(ctx, userObjectID, createdUser.Email); err != nil {
			// Log error but continue with registration
			// TODO: Consider using structured logging here
		}
	}

	userToken, err := token.Create(createdUser)
	if err != nil {
		return nil, errors.NewInternalError(errors.Service, "failed to create token", errors.KeyAuthErrorFailedToCreateToken, err)
	}

	return userToken, nil
}

func (s *service) LegacyRegister(ctx context.Context, user *model.User, referralCode string) (*token.Token, error) {
	if err := user.PrepareCreate(); err != nil {
		return nil, err
	}

	if err := s.UserService.Create(ctx, user, referralCode); err != nil {
		return nil, err
	}
	createdUser, err := s.UserService.FindByEmail(ctx, user.Email)
	if err != nil {
		return nil, err
	}

	// Link any existing subscriptions to the newly created user
	userObjectID, err := primitive.ObjectIDFromHex(createdUser.ID)
	if err == nil {
		// Don't fail registration if subscription linking fails, just log it
		if err := s.BillingService.LinkExistingSubscriptionsToUser(ctx, userObjectID, createdUser.Email); err != nil {
			// Log error but continue with registration
			// TODO: Consider using structured logging here
		}
	}

	userToken, err := token.Create(createdUser)
	if err != nil {
		return nil, err
	}

	return userToken, nil
}

func (s *service) Login(ctx context.Context, user *model.User) (*model.User, *token.Token, error) {
	if err := user.PrepareLogin(); err != nil {
		return nil, nil, err
	}

	userData, err := s.UserService.FindByEmail(ctx, user.Email)
	if err != nil {
		return nil, nil, err
	}
	userData.ID = userData.ObjectID.Hex()

	if err = userData.VerifyPassword(userData.Password, user.Password); err != nil {
		return nil, nil, err
	}

	token, err := token.Create(userData)
	if err != nil {
		return nil, nil, err
	}

	return userData, token, nil
}

func (s *service) RefreshAuth(ctx context.Context, refreshToken string) (*model.User, *token.Token, error) {
	tokenDetails, err := token.GetClaimsFromRefreshToken(refreshToken)
	if err != nil {
		return nil, nil, err
	}

	if err = tokenDetails.Validate(); err != nil {
		return nil, nil, err
	}

	user, err := s.UserService.Find(ctx, tokenDetails.Uid)
	if err != nil {
		return nil, nil, err
	}
	user.ID = user.ObjectID.Hex()

	token, err := token.Create(user)
	if err != nil {
		return nil, nil, err
	}

	return user, token, nil
}

func (s *service) ForgotPassword(ctx context.Context, userEmail string) error {
	foundUser, err := s.UserService.FindByEmail(ctx, userEmail)
	if err != nil {
		return err
	}

	userToken, err := token.Standalone(foundUser)
	if err != nil {
		return err
	}

	appURL := os.Getenv("APP_URL")
	if appURL == "" {
		appURL = "http://localhost:8080" // Fallback for development
	}
	resetLink := appURL + "/nova-senha/" + url.QueryEscape(base64.StdEncoding.WithPadding(base64.NoPadding).EncodeToString([]byte(userToken)))

	if s.NotificationService.BrevoNotifier != nil {
		return s.NotificationService.BrevoNotifier.SendPasswordReset(ctx, foundUser.Email, foundUser.Name, resetLink)
	}

	return errors.NewInternalError(errors.Service, "brevo notifier not available", errors.KeyAuthErrorBrevoNotifierNotAvailable, nil)
}

func (s *service) ResetPassword(ctx context.Context, resetToken, newPassword string) error {
	decodedToken, err := base64.StdEncoding.WithPadding(base64.NoPadding).DecodeString(resetToken)
	if err != nil {
		return err
	}

	details, err := token.GetClaimsFromToken(string(decodedToken[:]))
	if err != nil {
		return err
	}

	foundUser, err := s.UserService.Find(ctx, details.Uid)
	if err != nil {
		return err
	}

	// Copying foundUser data to newUser and then changing the password
	newUser := *foundUser
	newUser.Password = newPassword

	if err = foundUser.PrepareUpdate(&newUser); err != nil {
		return err
	}

	if err = s.UserService.Patch(ctx, foundUser, &newUser); err != nil {
		return err
	}

	return nil
}

func (s *service) CheckPassword(ctx context.Context, userUid, password string) error {
	foundUser, err := s.UserService.Find(ctx, userUid)
	if err != nil {
		return err
	}

	return foundUser.VerifyPassword(foundUser.Password, password)
}

// Admin Auth
func (s *service) AdminLogin(ctx context.Context, user *model.User) (*token.Token, error) {
	if err := user.PrepareLogin(); err != nil {
		return nil, err
	}

	foundUser, err := s.UserService.FindByEmail(ctx, user.Email)
	if err != nil {
		return nil, err
	}

	if err = foundUser.VerifyPassword(foundUser.Password, user.Password); err != nil {
		return nil, err
	}

	if !foundUser.IsAdmin() {
		return nil, errors.NewUnauthorizedError(errors.Service, "user is not an admin", errors.KeyAuthErrorUserNotAdmin, nil)
	}

	userToken, err := token.Create(foundUser)
	if err != nil {
		return nil, err
	}

	return userToken, nil
}

// HR Auth
func (s *service) HRLogin(ctx context.Context, user *model.User) (*token.Token, error) {
	if err := user.PrepareLogin(); err != nil {
		return nil, err
	}

	foundUser, err := s.UserService.FindByEmail(ctx, user.Email)
	if err != nil {
		return nil, err
	}

	if err = foundUser.VerifyPassword(foundUser.Password, user.Password); err != nil {
		return nil, err
	}

	if !auth.IsHumanResources(foundUser.Roles) {
		return nil, errors.NewUnauthorizedError(errors.Service, "user is not an HR", errors.KeyAuthErrorUserNotHR, nil)
	}

	userToken, err := token.Create(foundUser)
	if err != nil {
		return nil, err
	}

	return userToken, nil
}

// Session and OTP Auth
func (s *service) GetStatusByPhone(ctx context.Context, phone string) (string, string, error) {
	// First, check for a valid, non-expired session token
	session, err := s.SessionRepository.FindValidByPhone(ctx, phone)
	if err == nil && session.IsValid() {
		return "AUTHENTICATED", session.Token, nil
	}

	// If no valid session, check for a pending OTP
	otp, err := s.OTPRepository.FindValidByPhone(ctx, phone)
	if err == nil && otp.IsValid() {
		return "PENDING_VERIFICATION", "", nil
	}

	// If no users found with this phone number
	_, err = s.UserService.FindByPhone(ctx, phone)
	if err != nil {
		return "NOT_FOUND", "", nil
	}

	// If neither session nor OTP exists
	return "UNKNOWN", "", nil
}

func (s *service) GenerateOTP(ctx context.Context, phone string) error {
	// Validate phone number format (basic validation)
	if phone == "" {
		return errors.NewValidationError(errors.Service, "phone number is required", errors.KeyAuthErrorPhoneRequired, nil)
	}

	// Validate phone number format (advanced validation)
	if !isValidPhone(phone, "BR") { // TODO: Make region configurable
		return errors.NewValidationError(errors.Service, "phone number format is invalid", errors.KeyAuthErrorInvalidPhoneFormat, nil)
	}

	// Generate a secure 6-digit OTP using crypto/rand
	otpCode, err := s.generateSecureOTP()
	if err != nil {
		return errors.NewInternalError(errors.Service, "failed to generate OTP", errors.KeyAuthErrorOTPGenerationFailed, err)
	}

	// Hash the OTP using bcrypt
	hashedOTP, err := s.hashOTP(otpCode)
	if err != nil {
		return errors.NewInternalError(errors.Service, "failed to hash OTP", errors.KeyAuthErrorOTPGenerationFailed, err)
	}

	// Create OTP record with 5-minute expiration
	otpRecord := &auth.OTP{
		Phone:     phone,
		Code:      hashedOTP,
		ExpiresAt: time.Now().Add(5 * time.Minute),
	}

	// Upsert the OTP (overwrite any existing OTP for this phone)
	if err := s.OTPRepository.Upsert(ctx, otpRecord); err != nil {
		return errors.NewInternalError(errors.Service, "failed to store OTP", errors.KeyAuthErrorOTPGenerationFailed, err)
	}

	// Send OTP via email
	// TODO: Implement SMS sending in the future
	if err := s.sendOTPEmail(ctx, phone, otpCode); err != nil {
		// Log the error but don't fail the request - OTP was generated successfully
		// In production, you might want to handle this differently
		log.Printf("Failed to send OTP email: %v", err)
		return errors.NewInternalError(errors.Service, "OTP generated but failed to send email", errors.KeyAuthErrorOTPGenerationFailed, err)
	}

	return nil
}

func (s *service) VerifyOTP(ctx context.Context, phone, code string) (string, error) {
	// Validate input
	if phone == "" {
		return "", errors.NewValidationError(errors.Service, "phone number is required", errors.KeyAuthErrorPhoneRequired, nil)
	}
	if code == "" {
		return "", errors.NewValidationError(errors.Service, "OTP code is required", errors.KeyAuthErrorOTPCodeRequired, nil)
	}

	// Find the pending OTP
	otp, err := s.OTPRepository.FindValidByPhone(ctx, phone)
	if err != nil {
		// Return generic error message to avoid leaking information
		return "", errors.NewUnauthorizedError(errors.Service, "Invalid or expired verification code", errors.KeyAuthErrorInvalidOTPCode, err)
	}

	// Check if OTP is expired
	if otp.IsExpired() {
		// Delete expired OTP
		s.OTPRepository.DeleteByPhone(ctx, phone)
		return "", errors.NewUnauthorizedError(errors.Service, "Invalid or expired verification code", errors.KeyAuthErrorInvalidOTPCode, nil)
	}

	// Verify the code using bcrypt
	if err := bcrypt.CompareHashAndPassword([]byte(otp.Code), []byte(code)); err != nil {
		// Code mismatch - return generic error
		return "", errors.NewUnauthorizedError(errors.Service, "Invalid or expired verification code", errors.KeyAuthErrorInvalidOTPCode, err)
	}

	// Success! Clean up and create session
	// Delete the OTP record (one-time use only)
	if err := s.OTPRepository.DeleteByPhone(ctx, phone); err != nil {
		// Log error but don't fail the verification
		log.Printf("Failed to delete OTP after verification: %v", err)
	}

	// Find or create a user for this phone number
	userID, err := s.findOrCreatePhoneUser(ctx, phone)
	if err != nil {
		return "", errors.NewInternalError(errors.Service, "failed to create user session", errors.KeyAuthErrorOTPVerificationFailed, err)
	}

	// Generate session token
	sessionToken, err := token.CreateSessionToken(userID, phone)
	if err != nil {
		return "", errors.NewInternalError(errors.Service, "failed to create session token", errors.KeyAuthErrorOTPVerificationFailed, err)
	}

	var expirationMinutes int64 = 15
	if os.Getenv("TOKEN_EXPIRATION_MINUTES") != "" {
		expirationMinutes, _ = strconv.ParseInt(os.Getenv("TOKEN_EXPIRATION_MINUTES"), 10, 64)
	}

	// Store the session in the database
	session := &auth.Session{
		Phone:     phone,
		UserID:    userID,
		Token:     sessionToken,
		ExpiresAt: time.Now().Add(time.Duration(expirationMinutes) * time.Minute), // Same as token expiration
	}

	if err := s.SessionRepository.Create(ctx, session); err != nil {
		// Log error but still return the token since it was created successfully
		log.Printf("Failed to store session in database: %v", err)
	}

	return sessionToken, nil
}

// findOrCreatePhoneUser finds an existing user by phone or creates a minimal user record
func (s *service) findOrCreatePhoneUser(ctx context.Context, phone string) (string, error) {
	// Try to find existing user with this phone number
	// Error if the no users are found we are communicating with him via phone, so we are unable to get this information without it.
	user, err := s.UserService.FindByPhone(ctx, phone)
	if err != nil {
		return "", errors.NewNotFoundError(errors.Service, "no user found with this phone number", errors.KeyAuthErrorNotFoundByPhone, nil)
	}

	return user.ID, nil
}

func isValidPhone(phone, region string) bool {
	num, err := phonenumbers.Parse(phone, region) // e.g., "BR" for Brazil, "US" for United States
	if err != nil {
		return false
	}
	return phonenumbers.IsValidNumber(num)
}

// generateSecureOTP generates a cryptographically secure 6-digit OTP
func (s *service) generateSecureOTP() (string, error) {
	// Generate a random number between 100000 and 999999
	min := big.NewInt(100000)
	max := big.NewInt(999999)

	// Calculate the range
	rangeSize := new(big.Int).Sub(max, min)
	rangeSize.Add(rangeSize, big.NewInt(1)) // Add 1 to make it inclusive

	// Generate random number in range
	randomNum, err := rand.Int(rand.Reader, rangeSize)
	if err != nil {
		return "", err
	}

	// Add minimum to get final number
	result := new(big.Int).Add(randomNum, min)

	return result.String(), nil
}

// hashOTP hashes the OTP using bcrypt
func (s *service) hashOTP(otp string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(otp), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedBytes), nil
}

// sendOTPEmail sends the OTP via email
// TODO: Implement SMS sending in the future
func (s *service) sendOTPEmail(ctx context.Context, phone, otpCode string) error {
	// Get all user email from database
	user, err := s.UserService.FindByPhone(ctx, phone)
	if err != nil {
		return err
	}

	logoURL := "https://images.dinbora.com.br/logos/dinbora-white.png"
	mascotURL := "https://images.dinbora.com.br/logos/dinbora-mascot.png"

	subject := "Seu código de verificação - Dinbora"
	content := fmt.Sprintf(`
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    <title>Seu Código de Acesso Dinbora</title>
</head>
<body style="margin: 0; padding: 0; background-color: #ECE3FF; font-family: 'Poppins', Arial, sans-serif; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;">
    <div style="max-width: 600px; margin: 40px auto; background-color: #ffffff; border-radius: 16px; overflow: hidden; box-shadow: 0 4px 10px rgba(0,0,0,0.05);">
        <div style="background-color: #8C52FF; padding: 20px; text-align: center;">
            <!-- You can use an image for your logo for better consistency -->
            <img src="%s" alt="Dinbora Logo" style="height: 30px; width: auto;">
        </div>
        <div style="padding: 30px 40px; text-align: center; color: #333;">
            <img src="%s" alt="Dinbora Mascot" style="height: 100px; width: auto; margin-bottom: 20px;">
            <h1 style="color: #1E1E1E; font-size: 24px; font-weight: 600; margin: 0 0 10px 0;">Seu código de acesso está aqui!</h1>
            <p style="color: #555555; font-size: 16px; line-height: 1.6;">Olá, %s!</p>
            <p style="color: #555555; font-size: 16px; line-height: 1.6;">Use o código abaixo para acessar sua conta Dinbora.</p>
            
            <div style="background-color: #ECE3FF; border: 1px dashed #D6BCFA; border-radius: 12px; padding: 20px; margin: 30px 0;">
                <p style="margin: 0; font-size: 14px; color: #8C52FF; letter-spacing: 1px; text-transform: uppercase; font-weight: 600;">Seu código</p>
                <p style="color: #8C52FF; font-size: 42px; font-weight: 700; margin: 10px 0 0 0; letter-spacing: 8px;">%s</p>
            </div>

            <p style="color: #777; font-size: 14px;">Este código expira em 5 minutos. Por segurança, nunca o compartilhe com ninguém.</p>
            
            <p style="margin-top: 30px; color: #555555;">Abraços,<br><strong style="color: #8C52FF;">Equipe Dinbora</strong></p>
        </div>
        <div style="background-color: #ECE3FF; text-align: center; padding: 20px; font-size: 12px; color: #9A73E8;">
            <p style="margin: 0;">Se você não solicitou este código, pode ignorar este e-mail com segurança.</p>
        </div>
    </div>
</body>
</html>`, logoURL, mascotURL, user.Name, otpCode)

	log.Println("Sending OTP to user: " + user.Email)
	// Try Brevo to send OTP
	if s.NotificationService.BrevoNotifier != nil {
		log.Println("Using Brevo to send OTP")
		_, _ = subject, content
		// For Brevo, we'll use a generic email since we don't have a specific OTP template
		// In production, you should create a dedicated OTP template in Brevo
		if err := s.NotificationService.BrevoNotifier.SendGenericEmail(ctx, user.Email, user.Name, subject, content); err != nil {
			return err
		}
	} else {
		return errors.NewInternalError(errors.Service, "no notification providers available", errors.KeyAuthErrorOTPGenerationFailed, nil)
	}

	return nil
}
