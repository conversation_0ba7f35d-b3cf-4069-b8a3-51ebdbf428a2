package progression

import (
	"context"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"math"
)

func filterAccessibleTrails(trailCards []*content.TrailCard, userClassification string) []*content.TrailCard {
	filteredCards := make([]*content.TrailCard, 0, len(trailCards))
	for _, card := range trailCards {
		if card.HasAccess(userClassification) {
			filteredCards = append(filteredCards, card)
		}
	}
	return filteredCards
}

//	New API
//
// createLessonCardFromSummary creates a lesson card directly from the summary data
func createLessonCardFromSummary(lessonContent *content.Lesson, trailSummary *progression.TrailSummary) *progression.LessonCard {
	// Get lesson progress from summary
	lessonProgress := trailSummary.LessonProgress[lessonContent.Identifier]

	var completed, rewarded bool
	var current string
	var available = true

	if lessonProgress != nil {
		completed = lessonProgress.Completed
		rewarded = lessonProgress.Rewarded
		current = lessonProgress.Current
	} else {
		// Check availability based on requirements
		available = isLessonAvailableInSummary(lessonContent, trailSummary)
	}

	return &progression.LessonCard{
		Name:       lessonContent.Name,
		Identifier: lessonContent.Identifier,
		Type:       "LESSON",
		Logo:       lessonContent.Logo,
		Color:      lessonContent.Color,
		Order:      lessonContent.Order,
		Completed:  completed,
		Available:  available,
		Current:    current,
		Rewarded:   rewarded,
	}
}

// createChallengeCardFromSummary creates a challenge card directly from the summary data
// createChallengeCardFromSummary creates a challenge card directly from the summary data
func createChallengeCardFromSummary(challengeContent *content.Challenge, trailSummary *progression.TrailSummary, trailContent *content.Trail) *progression.ChallengeCard {
	// Get challenge progress from summary
	challengeProgress := trailSummary.ChallengeProgress

	var completed, rewarded bool
	var current string
	var total uint8
	var available bool

	if challengeProgress != nil {
		completed = challengeProgress.Completed
		rewarded = challengeProgress.Rewarded
		current = challengeProgress.Current

		// Calculate total completion percentage based on completed phases
		if len(challengeProgress.Phases) > 0 {
			completedPhases := 0
			for _, phase := range challengeProgress.Phases {
				if phase.Completed {
					completedPhases++
				}
			}
			// Safe calculation to prevent integer overflow
			if completedPhases > 0 && len(challengeProgress.Phases) > 0 {
				// Use float64 for calculation to avoid overflow, then clamp result
				percentage := float64(completedPhases) * 100.0 / float64(len(challengeProgress.Phases))
				if percentage > float64(math.MaxUint8) {
					total = math.MaxUint8
				} else if percentage < 0 {
					total = 0
				} else {
					total = uint8(percentage)
				}
			}
		}
		available = true
	} else {
		// Check if challenge should be available (lessons completed)
		available = !challengeContent.Locked && areAllLessonsCompleted(trailContent, trailSummary)
	}

	return &progression.ChallengeCard{
		Name:        challengeContent.Name,
		Identifier:  challengeContent.Identifier,
		Type:        "CHALLENGE",
		Description: challengeContent.Description,
		Logo:        challengeContent.Logo,
		Color:       challengeContent.Color,
		Total:       total,
		Completed:   completed,
		Available:   available,
		Current:     current,
		Rewarded:    rewarded,
	}
}

// isLessonAvailableInSummary checks if a lesson is available based on requirements in the summary
func isLessonAvailableInSummary(lessonContent *content.Lesson, trailSummary *progression.TrailSummary) bool {
	if len(lessonContent.Requirements) == 0 {
		return true // No requirements, always available
	}

	// Check if all required lessons are completed
	for _, requirement := range lessonContent.Requirements {
		if lessonProgress, exists := trailSummary.LessonProgress[requirement]; !exists || !lessonProgress.Completed {
			return false
		}
	}

	return true
}

// areAllLessonsCompleted checks if all lessons in a trail are completed
func areAllLessonsCompleted(trailContent *content.Trail, trailSummary *progression.TrailSummary) bool {
	for _, lesson := range trailContent.Lessons {
		if lessonProgress, exists := trailSummary.LessonProgress[lesson.Identifier]; !exists || !lessonProgress.Completed {
			return false
		}
	}
	return true
}

// createTrailCardFromSummary creates a trail card directly from the summary data
// createTrailCardFromSummary creates a trail card directly from the summary data
func createTrailCardFromSummary(cardData *content.TrailCard, summary *progression.ProgressSummary) *progression.TrailCard {
	card := &progression.TrailCard{
		ID:         cardData.ID,
		Name:       cardData.Name,
		Identifier: cardData.Identifier,
		Level:      cardData.Level,
		Logo:       cardData.Logo,
		Color:      cardData.Color,
	}

	// Add progression data if available
	if trailSummary, exists := summary.Trails[cardData.ID]; exists {
		var progressionPercent uint8
		if trailSummary.ProgressPercent > math.MaxUint8 {
			progressionPercent = 100
		} else if trailSummary.ProgressPercent < 0 {
			progressionPercent = 0
		} else {
			progressionPercent = uint8(trailSummary.ProgressPercent)
		}

		card.Total = progressionPercent
		card.Available = true // If it exists in progress, it's available
		card.Current = trailSummary.CurrentItem
	} else {
		// Default values for new trails
		card.Available = true // Assume available unless requirements say otherwise
		// Check if trail has requirements that would make it unavailable
		if len(cardData.Requirements) > 0 {
			for _, reqID := range cardData.Requirements {
				if reqSummary, reqExists := summary.Trails[reqID]; !reqExists || reqSummary.ProgressPercent < 90 {
					card.Available = false
					break
				}
			}
		}
	}

	return card
}

// createChallengePhaseCardFromSummary creates a challenge phase card directly from the summary data
func createChallengePhaseCardFromSummary(phaseContent *content.ChallengePhase, trailSummary *progression.TrailSummary) *progression.ChallengePhaseCard {
	var completed, rewarded, available bool
	var current string
	var currentPoints int
	challengeProgress := trailSummary.ChallengeProgress
	// Get challenge progress from summary
	if challengeProgress != nil {
		// Get phase progress
		if phaseProgress, exists := challengeProgress.Phases[phaseContent.Identifier]; exists {
			completed = phaseProgress.Completed
			current = phaseProgress.Current
			currentPoints = phaseProgress.CurrentPoints
		}
		rewarded = completed
		available = true
	} else {
		available = isPhaseAvailableInSummary(phaseContent, trailSummary)
	}

	return &progression.ChallengePhaseCard{
		Name:          phaseContent.Name,
		Identifier:    phaseContent.Identifier,
		Logo:          phaseContent.Logo,
		Color:         phaseContent.Color,
		Order:         phaseContent.Order,
		Completed:     completed,
		Available:     available,
		Current:       current,
		Rewarded:      rewarded,
		CurrentPoints: currentPoints,
	}
}

// isPhaseAvailableInSummary checks if a challenge phase is available based on requirements
func isPhaseAvailableInSummary(phaseContent *content.ChallengePhase, trailSummary *progression.TrailSummary) bool {
	if len(phaseContent.Requirements) == 0 {
		return true
	}

	for _, requirement := range phaseContent.Requirements {
		if !isRequirementCompleted(requirement, trailSummary) {
			return false
		}
	}

	return true
}

func isRequirementCompleted(requirement string, trailSummary *progression.TrailSummary) bool {
	// Check challenge phase requirement
	if trailSummary.ChallengeProgress != nil {
		if phaseProgress, exists := trailSummary.ChallengeProgress.Phases[requirement]; exists {
			return phaseProgress.Completed
		}
	}

	// Check lesson requirement
	if lessonProgress, exists := trailSummary.LessonProgress[requirement]; exists {
		return lessonProgress.Completed
	}

	return false
}

func GetChallengePhaseChoicePoints(phaseIdentifier, contentIdentifier, choiceIdentifier string, challengeContent *content.Challenge) (int, error) {
	phase := challengeContent.GetPhase(phaseIdentifier)
	if phase != nil {
		return phase.GetPoints(contentIdentifier, choiceIdentifier), nil
	}
	return 0, nil
}

func hasCompletedAndRewardedSummary(summary *progression.ProgressSummary, event *progression.ProgressEvent) bool {
	if event.Action == progression.CompletedActionType {
		if trailSummary := summary.Trails[event.TrailID]; trailSummary != nil {
			if event.ItemType == progression.LessonProgressType {
				return isLessonCompleted(trailSummary, event)
			} else if event.ItemType == progression.ChallengeProgressType {
				return isChallengePhaseCompleted(trailSummary, event)
			}
		}
	}

	return false
}

func isLessonCompleted(trailSummary *progression.TrailSummary, event *progression.ProgressEvent) bool {
	return trailSummary.LessonProgress[event.ItemID] != nil && trailSummary.LessonProgress[event.ItemID].Completed
}

func isChallengePhaseCompleted(summary *progression.TrailSummary, event *progression.ProgressEvent) bool {
	return summary.ChallengeProgress != nil && summary.ChallengeProgress.Phases[event.ItemID] != nil && summary.ChallengeProgress.Phases[event.ItemID].Completed
}

func CreateEventFromBody(ctx context.Context, userID string, body *progression.ProgressionBody, trailContent *content.Trail) (*progression.ProgressEvent, error) {
	event := &progression.ProgressEvent{
		UserID:    userID,
		TrailID:   body.Trail,
		ItemID:    body.Module,
		ItemType:  progression.ProgressType(body.Type),
		ContentID: body.Content,
		Data: map[string]interface{}{
			"originalTimestamp": body.Timestamp,
			"legacyRequest":     true,
		},
	}

	// Determine action based on choice
	if body.Choice != nil {
		event.Choice = &progression.Choice{
			Identifier: body.Choice.Identifier,
			Next:       body.Choice.Next,
		}

		if progression.ProgressType(body.Type) == progression.ChallengeProgressType {
			var err error
			event.Choice.Points, err = GetChallengePhaseChoicePoints(body.Module, body.Content, body.Choice.Identifier, trailContent.Challenge)
			if err != nil {
				return nil, err
			}
		}

		if isRewardChoice(body.Choice.Next) {
			event.Action = progression.CompletedActionType
		} else {
			event.Action = progression.StartedActionType
		}
	} else {
		event.Action = progression.StartedActionType
	}

	return event, nil
}

func isRewardChoice(next string) bool {
	return next == string(progression.RewardTypeCoin)
}
