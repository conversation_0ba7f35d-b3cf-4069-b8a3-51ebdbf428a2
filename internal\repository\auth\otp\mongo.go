package otp

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/auth"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection *mongo.Collection
}

func NewMongoDB(database *mongo.Database) Repository {
	return &mongoDB{
		collection: database.Collection(repository.AUTH_OTPS_COLLECTION),
	}
}

// Reader methods

func (r *mongoDB) FindByPhone(ctx context.Context, phone string) (*auth.OTP, error) {
	var otp auth.OTP
	filter := bson.M{"phone": phone}
	opts := options.FindOne().SetSort(bson.D{{Key: "createdAt", Value: -1}}) // Get the most recent

	err := r.collection.FindOne(ctx, filter, opts).Decode(&otp)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "OTP not found", errors.KeyAuthErrorOTPNotFound, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "failed to find OTP by phone", errors.KeyAuthErrorOTPNotFound, err)
	}

	otp.ID = otp.ObjectID.Hex()
	return &otp, nil
}

func (r *mongoDB) FindValidByPhone(ctx context.Context, phone string) (*auth.OTP, error) {
	var otp auth.OTP
	filter := bson.M{
		"phone":     phone,
		"verified":  false,
		"expiresAt": bson.M{"$gt": time.Now()},
	}
	opts := options.FindOne().SetSort(bson.D{{Key: "createdAt", Value: -1}}) // Get the most recent

	err := r.collection.FindOne(ctx, filter, opts).Decode(&otp)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "valid OTP not found", errors.KeyAuthErrorOTPNotFound, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "failed to find valid OTP by phone", errors.KeyAuthErrorOTPNotFound, err)
	}

	otp.ID = otp.ObjectID.Hex()
	return &otp, nil
}

func (r *mongoDB) FindByPhoneAndCode(ctx context.Context, phone, code string) (*auth.OTP, error) {
	var otp auth.OTP
	filter := bson.M{
		"phone": phone,
		"code":  code,
	}

	err := r.collection.FindOne(ctx, filter).Decode(&otp)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "OTP not found", errors.KeyAuthErrorOTPNotFound, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "failed to find OTP by phone and code", errors.KeyAuthErrorOTPNotFound, err)
	}

	otp.ID = otp.ObjectID.Hex()
	return &otp, nil
}

// Writer methods

func (r *mongoDB) Create(ctx context.Context, otp *auth.OTP) error {
	if err := otp.PrepareCreate(); err != nil {
		return err
	}

	result, err := r.collection.InsertOne(ctx, otp)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to create OTP", errors.KeyAuthErrorOTPNotFound, err)
	}

	otp.ObjectID = result.InsertedID.(primitive.ObjectID)
	otp.ID = otp.ObjectID.Hex()
	return nil
}

func (r *mongoDB) Update(ctx context.Context, otp *auth.OTP) error {
	objectID, err := primitive.ObjectIDFromHex(otp.ID)
	if err != nil {
		return errors.NewValidationError(errors.Repository, "invalid OTP ID", errors.KeyAuthErrorOTPNotFound, err)
	}

	otp.UpdatedAt = time.Now()
	filter := bson.M{"_id": objectID}
	update := bson.M{"$set": otp}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to update OTP", errors.KeyAuthErrorOTPNotFound, err)
	}

	if result.MatchedCount == 0 {
		return errors.NewNotFoundError(errors.Repository, "OTP not found for update", errors.KeyAuthErrorOTPNotFound, nil)
	}

	return nil
}

func (r *mongoDB) Upsert(ctx context.Context, otp *auth.OTP) error {
	if err := otp.PrepareCreate(); err != nil {
		return err
	}

	filter := bson.M{"phone": otp.Phone}
	update := bson.M{
		"$set": bson.M{
			"phone":     otp.Phone,
			"code":      otp.Code,
			"expiresAt": otp.ExpiresAt,
			"verified":  otp.Verified,
			"updatedAt": otp.UpdatedAt,
		},
		"$setOnInsert": bson.M{
			"createdAt": otp.CreatedAt,
		},
	}

	opts := options.Update().SetUpsert(true)
	result, err := r.collection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to upsert OTP", errors.KeyAuthErrorOTPGenerationFailed, err)
	}

	if result.UpsertedID != nil {
		otp.ObjectID = result.UpsertedID.(primitive.ObjectID)
		otp.ID = otp.ObjectID.Hex()
	}

	return nil
}

func (r *mongoDB) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.NewValidationError(errors.Repository, "invalid OTP ID", errors.KeyAuthErrorOTPNotFound, err)
	}

	filter := bson.M{"_id": objectID}
	result, err := r.collection.DeleteOne(ctx, filter)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to delete OTP", errors.KeyAuthErrorOTPNotFound, err)
	}

	if result.DeletedCount == 0 {
		return errors.NewNotFoundError(errors.Repository, "OTP not found for deletion", errors.KeyAuthErrorOTPNotFound, nil)
	}

	return nil
}

func (r *mongoDB) DeleteByPhone(ctx context.Context, phone string) error {
	filter := bson.M{"phone": phone}
	_, err := r.collection.DeleteMany(ctx, filter)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to delete OTPs by phone", errors.KeyAuthErrorOTPNotFound, err)
	}

	return nil
}

func (r *mongoDB) DeleteExpired(ctx context.Context) error {
	filter := bson.M{"expiresAt": bson.M{"$lt": time.Now()}}
	_, err := r.collection.DeleteMany(ctx, filter)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to delete expired OTPs", errors.KeyAuthErrorOTPExpired, err)
	}

	return nil
}
