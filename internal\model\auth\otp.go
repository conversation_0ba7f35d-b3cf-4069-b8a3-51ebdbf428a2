package auth

import (
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// OTP represents a pending OTP verification for phone authentication
type OTP struct {
	ObjectID  primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID        string             `json:"id,omitempty" bson:"-"`
	Phone     string             `json:"phone" bson:"phone" validate:"required"`
	Code      string             `json:"code" bson:"code" validate:"required"`
	ExpiresAt time.Time          `json:"expiresAt" bson:"expiresAt" validate:"required"`
	Verified  bool               `json:"verified" bson:"verified"`
	CreatedAt time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt time.Time          `json:"updatedAt" bson:"updatedAt"`
}

// PrepareCreate prepares the OTP for creation
func (o *OTP) PrepareCreate() error {
	now := time.Now()
	o.CreatedAt = now
	o.UpdatedAt = now
	o.Verified = false

	if err := o.validateCreate(); err != nil {
		return err
	}

	return nil
}

// validateCreate validates the OTP data for creation
func (o *OTP) validateCreate() error {
	if o.Phone == "" {
		return errors.NewValidationError(errors.Model, "phone is required", errors.KeyAuthErrorPhoneRequired, nil)
	}

	if o.Code == "" {
		return errors.NewValidationError(errors.Model, "OTP code is required", errors.KeyAuthErrorOTPCodeRequired, nil)
	}

	if o.ExpiresAt.IsZero() {
		return errors.NewValidationError(errors.Model, "expiration time is required", errors.KeyAuthErrorExpirationRequired, nil)
	}

	return nil
}

// IsExpired checks if the OTP has expired
func (o *OTP) IsExpired() bool {
	return time.Now().After(o.ExpiresAt)
}

// IsValid checks if the OTP is valid (not expired and not verified)
func (o *OTP) IsValid() bool {
	return !o.IsExpired() && !o.Verified
}

// MarkAsVerified marks the OTP as verified
func (o *OTP) MarkAsVerified() {
	o.Verified = true
	o.UpdatedAt = time.Now()
}
