package financialsheet

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
)

// MoneySourceInfo represents the JSON structure for money source information
type MoneySourceInfo struct {
	Identifier string       `json:"identifier"`
	Name       string       `json:"name"`
	Icon       CategoryIcon `json:"icon"`
}

// MoneySource represents the source of money for a transaction
type MoneySource byte

const (
	MoneySourceUndefined MoneySource = iota

	MoneySourceOpt1
	MoneySourceOpt2
	MoneySourceOpt3
	MoneySourceOpt4
	MoneySourceOpt5

	MoneySourceOther

	// Hidden money source for networth tracking (not visible to users)
	MoneySourceNetworthTracking
)

// IsValid validates the money source value
func (ms MoneySource) IsValid() bool {
	return ms >= MoneySourceOpt1 && ms <= MoneySourceNetworthTracking
}

func (ms MoneySource) Validate() error {
	if !ms.IsValid() {
		return errors.NewValidationError(errors.Model, "invalid money source value", errors.KeyFinancialSheetErrorInvalidMoneySource, nil)
	}
	return nil
}

// GetMoneySourceInfo returns both identifier and display name for a MoneySource based on the category context
func (c Category) GetMoneySourceInfo(ms MoneySource) MoneySourceInfo {
	// Extract base identifier if it's in the format "base_number"
	baseIdentifier := c.Identifier
	parts := strings.Split(string(c.Identifier), "_")
	if len(parts) > 1 {
		// Try to parse the last part as a number
		if _, err := strconv.Atoi(parts[len(parts)-1]); err == nil {
			// If successful, use everything before the last underscore as the base identifier
			baseIdentifier = CategoryIdentifier(strings.Join(parts[:len(parts)-1], "_"))
		}
	}

	switch baseIdentifier {
	case CategoryIdentifierCompensation:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"compensation_formal", "Salário CLT", CategoryIconCompensationFormal}
		case MoneySourceOpt2:
			return MoneySourceInfo{"compensation_contract", "Contrato PJ", CategoryIconCompensationContract}
		case MoneySourceOpt3:
			return MoneySourceInfo{"compensation_prolabore", "Pró-labore", CategoryIconCompensationProlabore}
		case MoneySourceOpt4:
			return MoneySourceInfo{"compensation_comission", "Comissões", CategoryIconCompensationComission}
		case MoneySourceOpt5:
			return MoneySourceInfo{"compensation_bonus", "Bônus", CategoryIconCompensationBonus}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconCompensationOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierBenefits:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"benefits_retirement", "Aposentadoria", CategoryIconBenefitsRetirement}
		case MoneySourceOpt2:
			return MoneySourceInfo{"benefits_government", "Bolsa Família", CategoryIconBenefitsGovernment}
		case MoneySourceOpt3:
			return MoneySourceInfo{"benefits_survivors_pension", "Pensão", CategoryIconBenefitsSurvivorsPension}
		case MoneySourceOpt4:
			return MoneySourceInfo{"benefits_meal_voucher", "Vale Refeição", CategoryIconBenefitsMealVoucher}
		case MoneySourceOpt5:
			return MoneySourceInfo{"benefits_transportation_voucher", "Vale Transporte", CategoryIconBenefitsTransportationVoucher}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconBenefitsOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierInvestmentIncome:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"investment_income_dividends", "Dividendos", CategoryIconInvestmentIncomeDividends}
		case MoneySourceOpt2:
			return MoneySourceInfo{"investment_income_fees", "Juros", CategoryIconInvestmentIncomeFees}
		case MoneySourceOpt3:
			return MoneySourceInfo{"investment_income_redemption", "Resgate", CategoryIconInvestmentIncomeRedemption}
		case MoneySourceOpt4:
			return MoneySourceInfo{"investment_income_rental_income", "Renda de Aluguel", CategoryIconInvestmentIncomeRentalIncome}
		case MoneySourceOpt5:
			return MoneySourceInfo{"investment_income_refund", "Restituição", CategoryIconInvestmentIncomeRefund}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconInvestmentIncomeOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierAdditionalEarnings:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"additional_earnings_sale_of_goods", "Venda de bens", CategoryIconAdditionalEarningsSaleOfGoods}
		case MoneySourceOpt2:
			return MoneySourceInfo{"additional_earnings_social_media_monetization", "Redes sociais", CategoryIconAdditionalEarningsSocialMediaMonetization}
		case MoneySourceOpt3:
			return MoneySourceInfo{"additional_earnings_cashback", "Cashback", CategoryIconAdditionalEarningsCashback}
		case MoneySourceOpt4:
			return MoneySourceInfo{"additional_earnings_prizes", "Prêmios", CategoryIconAdditionalEarningsPrizes}
		case MoneySourceOpt5:
			return MoneySourceInfo{"additional_earnings_inheritance", "Herança", CategoryIconAdditionalEarningsInheritance}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconAdditionalEarningsOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierPersonalNeeds:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"personal_needs_tithe", "Dízimo", CategoryIconPersonalNeedsTithe}
		case MoneySourceOpt2:
			return MoneySourceInfo{"personal_needs_donation", "Doação", CategoryIconPersonalNeedsDonation}
		case MoneySourceOpt3:
			return MoneySourceInfo{"personal_needs_clothes", "Roupas", CategoryIconPersonalNeedsClothes}
		case MoneySourceOpt4:
			return MoneySourceInfo{"personal_needs_furniture", "Móveis", CategoryIconPersonalNeedsFurniture}
		case MoneySourceOpt5:
			return MoneySourceInfo{"personal_needs_home_appliances", "Eletrodomésticos", CategoryIconPersonalNeedsHomeAppliances}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconPersonalNeedsOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierDreams:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"short_term_dreams", "Curto prazo", CategoryIconShortTermDreams}
		case MoneySourceOpt2:
			return MoneySourceInfo{"medium_term_dreams", "Médio prazo", CategoryIconMediumTermDreams}
		case MoneySourceOpt3:
			return MoneySourceInfo{"long_term_dreams", "Longo prazo", CategoryIconLongTermDreams}
		case MoneySourceOpt4:
			return MoneySourceInfo{"other", "Outros", CategoryIconDreamsOthers}
		case MoneySourceOpt5:
			return MoneySourceInfo{"other", "Outros", CategoryIconDreamsOthers}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconDreamsOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierDebts:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"debts_mortgage", "Financiamento Imobiliário", CategoryIconDebtsMortgage}
		case MoneySourceOpt2:
			return MoneySourceInfo{"debts_car_financing", "Financiamento Automóvel", CategoryIconDebtsCarFinancing}
		case MoneySourceOpt3:
			return MoneySourceInfo{"debts_credit_card", "Cartão de Crédito", CategoryIconDebtsCreditCard}
		case MoneySourceOpt4:
			return MoneySourceInfo{"debts_overdraft", "Cheque Especial", CategoryIconDebtsOverdraft}
		case MoneySourceOpt5:
			return MoneySourceInfo{"debts_personal_loan", "Crédito Pessoal", CategoryIconDebtsPersonalLoan}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconDebtsOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierPersonalReserves:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"personal_reserves_asset_acquisition", "Aquisição Patrimonial", CategoryIconPersonalReservesAssetAcquisition}
		case MoneySourceOpt2:
			return MoneySourceInfo{"personal_reserves_retirement", "Previdência", CategoryIconPersonalReservesRetirement}
		case MoneySourceOpt3:
			return MoneySourceInfo{"personal_reserves_strategic", "Reserva Estratégica", CategoryIconPersonalReservesStrategic}
		case MoneySourceOpt4:
			return MoneySourceInfo{"personal_reserves_investments", "Investimentos", CategoryIconPersonalReservesInvestments}
		case MoneySourceOpt5:
			return MoneySourceInfo{"personal_reserves_life_insurance", "Seguro de Vida", CategoryIconPersonalReservesLifeInsurance}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconPersonalReservesOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierHousing:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"housing_rent_mortgage", "Aluguel", CategoryIconHousingRentMortgage}
		case MoneySourceOpt2:
			return MoneySourceInfo{"housing_condo_fees", "Condomínio", CategoryIconHousingCondoFees}
		case MoneySourceOpt3:
			return MoneySourceInfo{"housing_maintenance", "Manutenção", CategoryIconHousingMaintenance}
		case MoneySourceOpt4:
			return MoneySourceInfo{"housing_insurance", "Seguro Residencial", CategoryIconHousingInsurance}
		case MoneySourceOpt5:
			return MoneySourceInfo{"other", "Outros", CategoryIconHousingOthers}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconHousingOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierHouseBills:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"house_bills_water", "Conta de Água", CategoryIconHouseBillsWater}
		case MoneySourceOpt2:
			return MoneySourceInfo{"house_bills_electricity", "Conta de Luz", CategoryIconHouseBillsElectricity}
		case MoneySourceOpt3:
			return MoneySourceInfo{"house_bills_gas", "Conta de Gás", CategoryIconHouseBillsGas}
		case MoneySourceOpt4:
			return MoneySourceInfo{"house_bills_collection_fees", "Taxas de Coleta", CategoryIconHouseBillsCollectionFees}
		case MoneySourceOpt5:
			return MoneySourceInfo{"other", "Outros", CategoryIconHouseBillsOthers}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconHouseBillsOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierEducation:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"education_formal", "Ensino Formal", CategoryIconEducationFormal}
		case MoneySourceOpt2:
			return MoneySourceInfo{"education_free_courses", "Cursos Livres", CategoryIconEducationFreeCourses}
		case MoneySourceOpt3:
			return MoneySourceInfo{"education_languages", "Idiomas", CategoryIconEducationLanguages}
		case MoneySourceOpt4:
			return MoneySourceInfo{"education_platforms", "Plataformas Educacionais", CategoryIconEducationPlatforms}
		case MoneySourceOpt5:
			return MoneySourceInfo{"education_books_materials", "Livros e Materiais", CategoryIconEducationBooksMaterials}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconEducationOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierFamily:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"family_allowance", "Mesada", CategoryIconFamilyAllowance}
		case MoneySourceOpt2:
			return MoneySourceInfo{"family_pets", "Pets", CategoryIconFamilyPets}
		case MoneySourceOpt3:
			return MoneySourceInfo{"family_gifts", "Presentes", CategoryIconFamilyGifts}
		case MoneySourceOpt4:
			return MoneySourceInfo{"family_assistance", "Assistência a Familiares", CategoryIconFamilyAssistance}
		case MoneySourceOpt5:
			return MoneySourceInfo{"family_events", "Eventos Familiares", CategoryIconFamilyEvents}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconFamilyOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierCommunication:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"communication_internet", "Internet", CategoryIconCommunicationInternet}
		case MoneySourceOpt2:
			return MoneySourceInfo{"communication_mobile_phone", "Telefone Celular", CategoryIconCommunicationMobilePhone}
		case MoneySourceOpt3:
			return MoneySourceInfo{"communication_cable_tv", "TV por Assinatura", CategoryIconCommunicationCableTV}
		case MoneySourceOpt4:
			return MoneySourceInfo{"communication_streaming", "Streaming", CategoryIconCommunicationStreaming}
		case MoneySourceOpt5:
			return MoneySourceInfo{"communication_digital_subscriptions", "Assinaturas Digitais", CategoryIconCommunicationDigitalSubscriptions}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconCommunicationOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierTransportation:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"transportation_tickets", "Passagens", CategoryIconTransportationTickets}
		case MoneySourceOpt2:
			return MoneySourceInfo{"transportation_mobility_apps", "Apps de Mobilidade", CategoryIconTransportationMobilityApps}
		case MoneySourceOpt3:
			return MoneySourceInfo{"transportation_fuel", "Combustível", CategoryIconTransportationFuel}
		case MoneySourceOpt4:
			return MoneySourceInfo{"transportation_car_insurance", "Seguro do Carro", CategoryIconTransportationCarInsurance}
		case MoneySourceOpt5:
			return MoneySourceInfo{"transportation_car_maintenance", "Manutenção do Carro", CategoryIconTransportationCarMaintenance}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconTransportationOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierHealth:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"health_healthcare_plan", "Convênio", CategoryIconHealthHealthcarePlan}
		case MoneySourceOpt2:
			return MoneySourceInfo{"health_medications", "Medicamentos", CategoryIconHealthMedications}
		case MoneySourceOpt3:
			return MoneySourceInfo{"health_dentist", "Dentista", CategoryIconHealthDentist}
		case MoneySourceOpt4:
			return MoneySourceInfo{"health_doctor", "Médico", CategoryIconHealthDoctor}
		case MoneySourceOpt5:
			return MoneySourceInfo{"health_medical_tests", "Exames", CategoryIconHealthMedicalTests}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconHealthOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierPersonalCare:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"personal_care_salon_barber", "Salão ou Barbearia", CategoryIconPersonalCareSalonBarber}
		case MoneySourceOpt2:
			return MoneySourceInfo{"personal_care_gym", "Academia", CategoryIconPersonalCareGym}
		case MoneySourceOpt3:
			return MoneySourceInfo{"personal_care_supplements", "Suplementos", CategoryIconPersonalCareSupplements}
		case MoneySourceOpt4:
			return MoneySourceInfo{"personal_care_therapy", "Terapia", CategoryIconPersonalCareTherapy}
		case MoneySourceOpt5:
			return MoneySourceInfo{"personal_care_hygiene", "Higiene Pessoal", CategoryIconPersonalCareHygiene}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconPersonalCareOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierFood:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"food_supermarket", "Supermercado", CategoryIconFoodSupermarket}
		case MoneySourceOpt2:
			return MoneySourceInfo{"food_farmers_market", "Feira", CategoryIconFoodFarmersMarket}
		case MoneySourceOpt3:
			return MoneySourceInfo{"food_butcher_shop", "Açougue", CategoryIconFoodButcherShop}
		case MoneySourceOpt4:
			return MoneySourceInfo{"food_bakery", "Padaria", CategoryIconFoodBakery}
		case MoneySourceOpt5:
			return MoneySourceInfo{"food_delivery", "Delivery", CategoryIconFoodDelivery}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconFoodOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierLeisure:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"leisure_travel", "Viagens", CategoryIconLeisureTravel}
		case MoneySourceOpt2:
			return MoneySourceInfo{"leisure_events", "Eventos", CategoryIconLeisureEvents}
		case MoneySourceOpt3:
			return MoneySourceInfo{"leisure_cinema", "Cinema", CategoryIconLeisureCinema}
		case MoneySourceOpt4:
			return MoneySourceInfo{"leisure_parties", "Festas", CategoryIconLeisureParties}
		case MoneySourceOpt5:
			return MoneySourceInfo{"leisure_bars_restaurants", "Bares e Restaurantes", CategoryIconLeisureBarsRestaurants}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconLeisureOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierCasualShopping:
		switch ms {
		case MoneySourceOpt1:
			return MoneySourceInfo{"casual_shopping_clothing_footwear", "Roupas e Calçados", CategoryIconCasualShoppingClothingFootwear}
		case MoneySourceOpt2:
			return MoneySourceInfo{"casual_shopping_accessories", "Acessórios", CategoryIconCasualShoppingAccessories}
		case MoneySourceOpt3:
			return MoneySourceInfo{"casual_shopping_electronics", "Eletrônicos", CategoryIconCasualShoppingElectronics}
		case MoneySourceOpt4:
			return MoneySourceInfo{"casual_shopping_cosmetics", "Cosméticos", CategoryIconCasualShoppingCosmetics}
		case MoneySourceOpt5:
			return MoneySourceInfo{"casual_shopping_laundry", "Lavanderia", CategoryIconCasualShoppingLaundry}
		case MoneySourceOther:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconCasualShoppingOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryIdentifierNewCategory:
		switch ms {
		case MoneySourceOpt1:
			categoryInfo := c.GetCategoryInfo()
			return MoneySourceInfo{
				fmt.Sprintf("other_%s", c.Identifier.String()),
				fmt.Sprintf("%s - Outros", categoryInfo.Name),
				CategoryIconNewCategoryOthers,
			}
		default:
			return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	default:
		return MoneySourceInfo{"undefined", "Indefinido", CategoryIconUndefined}
	}
}
