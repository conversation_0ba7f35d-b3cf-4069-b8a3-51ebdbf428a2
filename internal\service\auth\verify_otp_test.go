package auth

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/auth"
	"golang.org/x/crypto/bcrypt"
)

func TestVerifyOTPValidation(t *testing.T) {
	service := &service{}
	ctx := context.Background()

	// Test empty phone
	_, err := service.VerifyOTP(ctx, "", "123456")
	if err == nil {
		t.Error("Expected error for empty phone")
	}

	// Test empty code
	_, err = service.VerifyOTP(ctx, "31991445883", "")
	if err == nil {
		t.Error("Expected error for empty code")
	}
}

func TestGenerateSecureOTPUniqueness(t *testing.T) {
	service := &service{}

	// Generate multiple OTPs to test randomness
	otps := make(map[string]bool)
	for i := 0; i < 100; i++ {
		otp, err := service.generateSecureOTP()
		if err != nil {
			t.Fatalf("generateSecureOTP failed: %v", err)
		}

		// Verify OTP format
		if len(otp) != 6 {
			t.Erro<PERSON>("Expected 6-digit OTP, got %s", otp)
		}

		// Check for duplicates (should be very rare)
		if otps[otp] {
			t.Logf("Duplicate OTP generated: %s (this is rare but possible)", otp)
		}
		otps[otp] = true
	}

	// Verify we got some variety (at least 50% unique)
	if len(otps) < 50 {
		t.Errorf("Expected more variety in OTP generation, got %d unique OTPs out of 100", len(otps))
	}
}

func TestHashOTPVerification(t *testing.T) {
	service := &service{}

	otp := "123456"
	hashedOTP, err := service.hashOTP(otp)
	if err != nil {
		t.Fatalf("hashOTP failed: %v", err)
	}

	if hashedOTP == "" {
		t.Error("Hashed OTP should not be empty")
	}

	if hashedOTP == otp {
		t.Error("Hashed OTP should be different from original")
	}

	// Verify the hash can be verified
	err = bcrypt.CompareHashAndPassword([]byte(hashedOTP), []byte(otp))
	if err != nil {
		t.Errorf("Hash verification failed: %v", err)
	}

	// Verify wrong code fails
	err = bcrypt.CompareHashAndPassword([]byte(hashedOTP), []byte("wrong-code"))
	if err == nil {
		t.Error("Hash verification should fail for wrong code")
	}
}

func TestOTPExpiration(t *testing.T) {
	// Test expired OTP
	expiredOTP := &auth.OTP{
		Phone:     "31991445883",
		Code:      "hashed-code",
		ExpiresAt: time.Now().Add(-1 * time.Minute), // Expired 1 minute ago
		Verified:  false,
	}

	if !expiredOTP.IsExpired() {
		t.Error("OTP should be expired")
	}

	// Test valid OTP
	validOTP := &auth.OTP{
		Phone:     "31991445883",
		Code:      "hashed-code",
		ExpiresAt: time.Now().Add(5 * time.Minute), // Expires in 5 minutes
		Verified:  false,
	}

	if validOTP.IsExpired() {
		t.Error("OTP should not be expired")
	}
}

func TestOTPValidation(t *testing.T) {
	// Test valid OTP
	validOTP := &auth.OTP{
		Phone:     "31991445883",
		Code:      "hashed-code",
		ExpiresAt: time.Now().Add(5 * time.Minute),
		Verified:  false,
	}

	if !validOTP.IsValid() {
		t.Error("OTP should be valid")
	}

	// Test expired OTP
	expiredOTP := &auth.OTP{
		Phone:     "31991445883",
		Code:      "hashed-code",
		ExpiresAt: time.Now().Add(-1 * time.Minute),
		Verified:  false,
	}

	if expiredOTP.IsValid() {
		t.Error("Expired OTP should not be valid")
	}

	// Test verified OTP
	verifiedOTP := &auth.OTP{
		Phone:     "31991445883",
		Code:      "hashed-code",
		ExpiresAt: time.Now().Add(5 * time.Minute),
		Verified:  true,
	}

	if verifiedOTP.IsValid() {
		t.Error("Verified OTP should not be valid for reuse")
	}
}

// Test the core bcrypt verification logic that VerifyOTP uses
func TestBcryptVerificationLogic(t *testing.T) {
	originalCode := "123456"

	// Hash the code
	hashedCode, err := bcrypt.GenerateFromPassword([]byte(originalCode), bcrypt.DefaultCost)
	if err != nil {
		t.Fatalf("Failed to hash code: %v", err)
	}

	// Test correct code verification
	err = bcrypt.CompareHashAndPassword(hashedCode, []byte(originalCode))
	if err != nil {
		t.Errorf("Correct code verification failed: %v", err)
	}

	// Test wrong code verification
	err = bcrypt.CompareHashAndPassword(hashedCode, []byte("wrong-code"))
	if err == nil {
		t.Error("Wrong code verification should fail")
	}

	// Test empty code verification
	err = bcrypt.CompareHashAndPassword(hashedCode, []byte(""))
	if err == nil {
		t.Error("Empty code verification should fail")
	}
}
