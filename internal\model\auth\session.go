package auth

import (
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Session represents a user session with phone authentication
type Session struct {
	ObjectID  primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID        string             `json:"id,omitempty" bson:"-"`
	Phone     string             `json:"phone" bson:"phone" validate:"required"`
	UserID    string             `json:"userId" bson:"userId" validate:"required"`
	Token     string             `json:"token" bson:"token" validate:"required"`
	ExpiresAt time.Time          `json:"expiresAt" bson:"expiresAt" validate:"required"`
	CreatedAt time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt time.Time          `json:"updatedAt" bson:"updatedAt"`
}

// PrepareCreate prepares the session for creation
func (s *Session) PrepareCreate() error {
	now := time.Now()
	s.CreatedAt = now
	s.UpdatedAt = now

	if err := s.validateCreate(); err != nil {
		return err
	}

	return nil
}

// validateCreate validates the session data for creation
func (s *Session) validateCreate() error {
	if s.Phone == "" {
		return errors.NewValidationError(errors.Model, "phone is required", errors.KeyAuthErrorPhoneRequired, nil)
	}

	if s.UserID == "" {
		return errors.NewValidationError(errors.Model, "user ID is required", errors.KeyAuthErrorUserIDRequired, nil)
	}

	if s.Token == "" {
		return errors.NewValidationError(errors.Model, "token is required", errors.KeyAuthErrorTokenRequired, nil)
	}

	if s.ExpiresAt.IsZero() {
		return errors.NewValidationError(errors.Model, "expiration time is required", errors.KeyAuthErrorExpirationRequired, nil)
	}

	return nil
}

// IsExpired checks if the session has expired
func (s *Session) IsExpired() bool {
	return time.Now().After(s.ExpiresAt)
}

// IsValid checks if the session is valid (not expired)
func (s *Session) IsValid() bool {
	return !s.IsExpired()
}
