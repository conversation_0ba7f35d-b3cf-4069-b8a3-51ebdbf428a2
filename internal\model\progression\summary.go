package progression

import (
	"time"
)

// ProgressSummary represents the summary of a user's progression.
type ProgressSummary struct {
	UserID    string                   `json:"userId" bson:"userId"`
	Trails    map[string]*TrailSummary `json:"trails" bson:"trails"`
	UpdatedAt time.Time                `json:"updatedAt" bson:"updatedAt"`
	Revision  int                      `json:"revision" bson:"revision"`
}

type AchievementSummary struct {
	ID        string    `json:"id"`
	Timestamp time.Time `json:"timestamp"`
}

// TrailSummary represents the summary of a user's progression for a specific trail.
type TrailSummary struct {
	ID                 string          `json:"id" bson:"id"`
	LessonsCompleted   map[string]bool `json:"lessonsCompleted" bson:"lessonsCompleted"`
	ChallengeCompleted bool            `json:"challengeCompleted" bson:"challengeCompleted"`
	CurrentItem        string          `json:"currentItem" bson:"currentItem"`
	ProgressPercent    int             `json:"progressPercent" bson:"progressPercent"`
	IsCompleted        bool            `json:"isCompleted" bson:"isCompleted"`
	TotalRewards       int             `json:"totalRewards" bson:"totalRewards"`
	LessonsRewarded    map[string]bool `json:"lessonsRewarded" bson:"lessonsRewarded"`
	ChallengeRewarded  bool            `json:"challengeRewarded" bson:"challengeRewarded"`

	// Detailed progress tracking
	LessonProgress    map[string]*LessonProgress `json:"lessonProgress" bson:"lessonProgress"`
	ChallengeProgress *ChallengeProgress         `json:"challengeProgress" bson:"challengeProgress"`
}

// LessonProgress represents the progress of a user for a specific lesson.
type LessonProgress struct {
	Current   string   `json:"current" bson:"current"`
	Completed bool     `json:"completed" bson:"completed"`
	Rewarded  bool     `json:"rewarded" bson:"rewarded"`
	Path      []string `json:"path" bson:"path"` // Content IDs visited
}

// ChallengeProgress represents the progress of a user for a specific challenge.
type ChallengeProgress struct {
	Current       string                    `json:"current" bson:"current"`
	Completed     bool                      `json:"completed" bson:"completed"`
	Rewarded      bool                      `json:"rewarded" bson:"rewarded"`
	CurrentPoints int                       `json:"currentPoints" bson:"currentPoints"`
	Phases        map[string]*PhaseProgress `json:"phases" bson:"phases"`
}

// PhaseProgress represents the progress of a user for a specific phase of a challenge.
type PhaseProgress struct {
	Current       string   `json:"current" bson:"current"`
	Completed     bool     `json:"completed" bson:"completed"`
	CurrentPoints int      `json:"currentPoints" bson:"currentPoints"`
	Path          []string `json:"path" bson:"path"`
}

func (ps *ProgressSummary) IsValid() bool {
	if !isValidObjectID(ps.UserID) {
		return false
	}
	for _, trail := range ps.Trails {
		if trail == nil || !trail.IsValid() {
			return false
		}
	}
	return true
}

func (ts *TrailSummary) IsValid() bool {
	if !isValidObjectID(ts.ID) {
		return false
	}
	for _, lp := range ts.LessonProgress {
		if lp == nil || !lp.IsValid() {
			return false
		}
	}

	if ts.ChallengeProgress != nil {
		return ts.ChallengeProgress.IsValid()
	}

	return true
}

func (lp *LessonProgress) IsValid() bool {
	return len(lp.Path) > 0
}

func (cp *ChallengeProgress) IsValid() bool {
	for _, phase := range cp.Phases {
		if phase == nil || !phase.IsValid() {
			return false
		}
	}
	return true
}

func (pp *PhaseProgress) IsValid() bool {
	return len(pp.Path) > 0
}

func NewTrailSummary(trailID string) *TrailSummary {
	return &TrailSummary{
		ID:               trailID,
		LessonsCompleted: make(map[string]bool),
		LessonsRewarded:  make(map[string]bool),
		LessonProgress:   make(map[string]*LessonProgress),
	}
}
