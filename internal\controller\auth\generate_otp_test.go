package auth

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
)

func TestGenerateOTPController(t *testing.T) {
	// Setup
	mockService := NewMockAuthService()
	controller := &controller{
		Service: mockService,
	}

	// Test successful OTP generation
	t.Run("Successful OTP Generation", func(t *testing.T) {
		e := echo.New()

		requestBody := map[string]string{
			"phone": "31991445883",
		}

		jsonBody, _ := json.Marshal(requestBody)
		req := httptest.NewRequest(http.MethodPost, "/api/v2/auth/generate-otp", bytes.NewBuffer(jsonBody))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		handler := controller.GenerateOTP()
		err := handler(c)

		if err != nil {
			t.Fatalf("Handler returned error: %v", err)
		}

		if rec.Code != http.StatusOK {
			t.<PERSON>rf("Expected status 200, got %d", rec.Code)
		}

		var response map[string]interface{}
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		if err != nil {
			t.Fatalf("Failed to unmarshal response: %v", err)
		}

		if response["status"] != "success" {
			t.Errorf("Expected status 'success', got %v", response["status"])
		}

		if response["message"] != "OTP generation process initiated." {
			t.Errorf("Expected message 'OTP generation process initiated.', got %v", response["message"])
		}
	})

	// Test missing phone number
	t.Run("Missing Phone Number", func(t *testing.T) {
		e := echo.New()

		requestBody := map[string]string{}

		jsonBody, _ := json.Marshal(requestBody)
		req := httptest.NewRequest(http.MethodPost, "/api/v2/auth/generate-otp", bytes.NewBuffer(jsonBody))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		handler := controller.GenerateOTP()
		err := handler(c)

		if err == nil {
			t.Error("Expected error for missing phone number")
		}
	})

	// Test invalid JSON
	t.Run("Invalid JSON", func(t *testing.T) {
		e := echo.New()

		req := httptest.NewRequest(http.MethodPost, "/api/v2/auth/generate-otp", bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		handler := controller.GenerateOTP()
		err := handler(c)

		if err == nil {
			t.Error("Expected error for invalid JSON")
		}
	})
}
