package financialsheet

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// TestCreateRecurringTransaction_NoGamificationLogic verifies that recurring transactions
// don't trigger gamification logic (points, streaks, league updates, explorer achievements)
// but still perform financial calculations correctly
func TestCreateRecurringTransaction_NoGamificationLogic(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}
	mockGamification := &MockGamificationService{}

	service := &service{
		Repository:          mockRepo,
		LeagueService:       mockLeague,
		GamificationService: mockGamification,
	}

	// Create a user with existing points/streak to verify they don't change
	initialPoints := financialsheet.Points{
		Current:             5,
		Best:                10,
		LastTransactionDate: time.Date(2025, 9, 10, 0, 0, 0, 0, time.UTC),
		MissedDays:          []time.Time{},
		NoTransactionDates:  []time.Time{},
	}

	record := &financialsheet.Record{
		ObjectID:           primitive.NewObjectID(),
		UserID:             "user123",
		UserName:           "Test User",
		Points:             initialPoints,
		YearData:           make(map[int]financialsheet.YearData),
		Balance:            monetary.Amount(0),
		TotalIncome:        monetary.Amount(0),
		TotalCostsOfLiving: monetary.Amount(0),
		TotalExpenses:      monetary.Amount(0),
	}

	transaction := &financialsheet.Transaction{
		Category:      "compensation",
		MoneySource:   financialsheet.MoneySourceOpt1,
		Value:         monetary.Amount(500000), // $5000
		Date:          time.Date(2025, 9, 15, 10, 0, 0, 0, time.UTC),
		PaymentMethod: financialsheet.PaymentMethodOpt1,
		Type:          financialsheet.CategoryTypeIncome,
	}

	recurrenceMonths := []int{10, 11, 12} // October, November, December

	userTimezone, _ := time.LoadLocation("America/Sao_Paulo")

	// Mock category lookup
	category := &financialsheet.Category{
		ID:         primitive.NewObjectID().Hex(),
		Identifier: "compensation",
		Name:       "Compensation",
		Type:       financialsheet.CategoryTypeIncome,
		User:       "user123",
	}

	// Set up expectations
	mockRepo.On("FindCategoryByUserAndIdentifier", ctx, "user123", financialsheet.CategoryIdentifier("compensation")).Return(category, nil)
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)

	// Expect only Planning achievement check (no Explorer achievement or League calls)
	mockGamification.On("CheckPlanningAchievement", ctx, "user123").Return(nil)

	// Act
	updatedRecord, err := service.CreateRecurringTransaction(ctx, record, transaction, recurrenceMonths, userTimezone)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, updatedRecord)

	// Verify gamification logic was NOT triggered (points/streaks unchanged)
	assert.Equal(t, initialPoints.Current, updatedRecord.Points.Current, "Points.Current should not change for recurring transactions")
	assert.Equal(t, initialPoints.Best, updatedRecord.Points.Best, "Points.Best should not change for recurring transactions")
	assert.Equal(t, initialPoints.LastTransactionDate, updatedRecord.Points.LastTransactionDate, "LastTransactionDate should not change for recurring transactions")

	// Verify financial calculations were performed correctly
	expectedTotalIncome := monetary.Amount(500000 * 4) // Original + 3 recurring = 4 transactions
	assert.Equal(t, expectedTotalIncome, updatedRecord.TotalIncome, "TotalIncome should include all recurring transactions")
	assert.Equal(t, expectedTotalIncome, updatedRecord.Balance, "Balance should be calculated correctly")

	// Verify all transactions were created (original + 3 recurring)
	totalTransactions := 0
	for _, yearData := range updatedRecord.YearData {
		for _, monthData := range yearData {
			totalTransactions += len(monthData.Transactions)
		}
	}
	assert.Equal(t, 4, totalTransactions, "Should have 4 transactions (1 original + 3 recurring)")

	// Verify that League service was NOT called (no investida recording for recurring transactions)
	mockLeague.AssertNotCalled(t, "RecordTransactionForAllUserLeagues")

	// Verify that only Planning achievement was checked (no Explorer achievement)
	mockGamification.AssertNotCalled(t, "CheckExplorerAchievement")
	mockGamification.AssertCalled(t, "CheckPlanningAchievement", ctx, "user123")

	// Verify repository calls
	mockRepo.AssertExpectations(t)
	mockGamification.AssertExpectations(t)
}

// TestCreateTransaction_WithGamificationLogic verifies that regular transactions
// (non-recurring) still trigger gamification logic correctly
func TestCreateTransaction_WithGamificationLogic(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}
	mockGamification := &MockGamificationService{}

	service := &service{
		Repository:          mockRepo,
		LeagueService:       mockLeague,
		GamificationService: mockGamification,
	}

	record := &financialsheet.Record{
		ObjectID:           primitive.NewObjectID(),
		UserID:             "user123",
		UserName:           "Test User",
		Points:             financialsheet.Points{Current: 0, Best: 0},
		YearData:           make(map[int]financialsheet.YearData),
		Balance:            monetary.Amount(0),
		TotalIncome:        monetary.Amount(0),
		TotalCostsOfLiving: monetary.Amount(0),
		TotalExpenses:      monetary.Amount(0),
	}

	transaction := &financialsheet.Transaction{
		Category:      "compensation",
		MoneySource:   financialsheet.MoneySourceOpt1,
		Value:         monetary.Amount(500000),
		Date:          time.Date(2025, 9, 15, 10, 0, 0, 0, time.UTC),
		PaymentMethod: financialsheet.PaymentMethodOpt1,
		Type:          financialsheet.CategoryTypeIncome,
	}

	userTimezone, _ := time.LoadLocation("America/Sao_Paulo")

	// Mock category lookup
	category := &financialsheet.Category{
		ID:         primitive.NewObjectID().Hex(),
		Identifier: "compensation",
		Name:       "Compensation",
		Type:       financialsheet.CategoryTypeIncome,
		User:       "user123",
	}

	// Set up expectations
	mockRepo.On("FindCategoryByUserAndIdentifier", ctx, "user123", financialsheet.CategoryIdentifier("compensation")).Return(category, nil)
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)

	// Expect all gamification calls for regular transactions
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time")).Return(nil)
	mockGamification.On("CheckExplorerAchievement", ctx, "user123").Return(nil)
	mockGamification.On("CheckPlanningAchievement", ctx, "user123").Return(nil)

	// Act
	updatedRecord, err := service.CreateTransaction(ctx, record, transaction, false, userTimezone) // isRecurring = false

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, updatedRecord)

	// Verify gamification logic WAS triggered (points/streaks updated)
	assert.Equal(t, 1, updatedRecord.Points.Current, "Points.Current should be updated for regular transactions")
	assert.Equal(t, 1, updatedRecord.Points.Best, "Points.Best should be updated for regular transactions")
	assert.False(t, updatedRecord.Points.LastTransactionDate.IsZero(), "LastTransactionDate should be updated for regular transactions")

	// Verify all gamification services were called
	mockLeague.AssertCalled(t, "RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time"))
	mockGamification.AssertCalled(t, "CheckExplorerAchievement", ctx, "user123")
	mockGamification.AssertCalled(t, "CheckPlanningAchievement", ctx, "user123")

	// Verify repository calls
	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)
	mockGamification.AssertExpectations(t)
}
