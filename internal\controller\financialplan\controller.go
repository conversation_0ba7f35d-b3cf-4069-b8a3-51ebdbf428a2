package financialplan

import (
	"context"
	"net/http"
	"strconv"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/service/financialplan"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// CRUD

	// Category CRUD

	// Transaction CRUD

	// Utility
	Summary() echo.HandlerFunc
}

type controller struct {
	Service financialplan.Service
}

func New(service financialplan.Service) Controller {
	return &controller{
		Service: service,
	}
}

func (fc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	financialsheetsGroup := currentGroup.Group("/financialplans", middlewares.AuthGuard())
	meGroup := financialsheetsGroup.Group("/me")

	// CRUD

	// Category CRUD

	// Transaction CRUD
	meGroup.GET("/transactions", fc.FindAllTransactions())

	// Utility
	meGroup.GET("/summary", fc.Summary())
}

func (fc *controller) FindAllTransactions() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Extract user ID from JWT token (set by AuthGuard middleware)
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return errors.NewUnauthorizedError(errors.Controller, "failed to extract user claims", errors.KeyFinancialPlanErrorUnauthorized, err)
		}

		// Get periodStr from query params
		periodStr := c.QueryParam("period")

		// Validate period parameter
		if !isValidPeriod(periodStr) {
			return errors.NewValidationError(errors.Controller, "invalid period parameter", errors.KeyFinancialPlanErrorInvalidPeriod, nil)
		}

		// Get integer month from period
		period, err := strconv.Atoi(strings.TrimSuffix(periodStr, "m"))
		if err != nil {
			return errors.NewValidationError(errors.Controller, "unable to parse period", errors.KeyFinancialPlanErrorInvalidPeriod, nil)
		}

		// Get category type from query params
		categoryType := c.QueryParam("type")

		// Validate category type if provided
		if categoryType != "" {
			if err := financialsheet.CategoryType(categoryType).Validate(); err != nil {
				return errors.NewValidationError(errors.Controller, "invalid category type", errors.KeyFinancialPlanErrorInvalidCategoryType, err)
			}
		}

		// Call service to fetch financial plan transactions
		transactions, err := fc.Service.FindAllTransactions(ctx, userToken.Uid, financialsheet.CategoryType(categoryType), period)
		if err != nil {
			return err
		}

		// Return the financial plan transactions
		return c.JSON(http.StatusOK, transactions)
	}
}

func (fc *controller) Summary() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Extract user ID from JWT token (set by AuthGuard middleware)
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return errors.NewUnauthorizedError(errors.Controller, "failed to extract user claims", errors.KeyFinancialPlanErrorUnauthorized, err)
		}

		// Get periodStr from query params
		periodStr := c.QueryParam("period")

		// Validate period parameter
		if !isValidPeriod(periodStr) {
			return errors.NewValidationError(errors.Controller, "invalid period parameter", errors.KeyFinancialPlanErrorInvalidPeriod, nil)
		}

		// Get integer month from period
		period, err := strconv.Atoi(strings.TrimSuffix(periodStr, "m"))
		if err != nil {
			return errors.NewValidationError(errors.Controller, "unable to parse period", errors.KeyFinancialPlanErrorInvalidPeriod, nil)
		}

		// Call service to fetch financial plan summary
		summary, err := fc.Service.Summary(ctx, userToken.Uid, period)
		if err != nil {
			return err
		}

		// Return the financial plan summary
		return c.JSON(http.StatusOK, summary)
	}
}

// Helper function to validate the period parameter
func isValidPeriod(period string) bool {
	validPeriods := map[string]bool{
		"1m":  true,
		"3m":  true,
		"6m":  true,
		"12m": true,
	}

	return validPeriods[period]
}
