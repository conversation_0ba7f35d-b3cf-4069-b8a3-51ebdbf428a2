package financialsheet

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// TestCreateTransaction_StreakWithNoTransactions tests the bug fix where
// CreateTransaction should continue the streak if intermediate days are covered by NoTransactions
func TestCreateTransaction_StreakWithNoTransactions(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	// Simulate scenario: User had transaction on Sept 1st, NoTransactions on Sept 2nd and 3rd,
	// now creating transaction on Sept 4th - streak should continue
	sept1 := time.Date(2025, 9, 1, 0, 0, 0, 0, time.UTC)
	sept2 := time.Date(2025, 9, 2, 0, 0, 0, 0, time.UTC)
	sept3 := time.Date(2025, 9, 3, 0, 0, 0, 0, time.UTC)
	sept4 := time.Date(2025, 9, 4, 0, 0, 0, 0, time.UTC)

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             5,  // Had a streak of 5
			Best:                10, // Best streak is 10
			LastTransactionDate: sept1,
			NoTransactionDates:  []time.Time{sept2, sept3}, // Covered Sept 2nd and 3rd with NoTransactions
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	transaction := &financialsheet.Transaction{
		ObjectID:      primitive.NewObjectID(),
		Category:      "food",
		MoneySource:   financialsheet.MoneySourceOpt1,
		Value:         monetary.Amount(5000),
		Date:          sept4, // Creating transaction on Sept 4th
		PaymentMethod: financialsheet.PaymentMethodOpt1,
		Type:          financialsheet.CategoryTypeCostsOfLiving,
	}

	// Mock expectations
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
	mockRepo.On("FindCategoryByUserAndIdentifier", ctx, "user123", mock.AnythingOfType("financialsheet.CategoryIdentifier")).Return(&financialsheet.Category{
		Identifier: "food",
		Name:       "Food",
		Type:       financialsheet.CategoryTypeCostsOfLiving,
	}, nil)
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time")).Return(nil)

	// Act
	updatedRecord, err := service.CreateTransaction(ctx, record, transaction, false, time.UTC)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, 6, updatedRecord.Points.Current, "Streak should continue from 5 to 6 because intermediate days were covered by NoTransactions")
	assert.Equal(t, 10, updatedRecord.Points.Best, "Best streak should remain 10")
	assert.Empty(t, updatedRecord.Points.MissedDays, "Should not have any missed days since all intermediate days were covered")
	assert.Equal(t, sept4, updatedRecord.Points.LastTransactionDate, "LastTransactionDate should be updated to Sept 4th")

	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)
}

// TestCreateTransaction_StreakBrokenWithMissedDays tests that streak is properly broken
// when there are actual missed days (not covered by NoTransactions)
func TestCreateTransaction_StreakBrokenWithMissedDays(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	// Simulate scenario: User had transaction on Sept 1st, NoTransactions on Sept 2nd only,
	// now creating transaction on Sept 4th - streak should break because Sept 3rd was missed
	sept1 := time.Date(2025, 9, 1, 0, 0, 0, 0, time.UTC)
	sept2 := time.Date(2025, 9, 2, 0, 0, 0, 0, time.UTC)
	sept4 := time.Date(2025, 9, 4, 0, 0, 0, 0, time.UTC)

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             5,  // Had a streak of 5
			Best:                10, // Best streak is 10
			LastTransactionDate: sept1,
			NoTransactionDates:  []time.Time{sept2}, // Only covered Sept 2nd, Sept 3rd was missed
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	transaction := &financialsheet.Transaction{
		ObjectID:      primitive.NewObjectID(),
		Category:      "food",
		MoneySource:   financialsheet.MoneySourceOpt1,
		Value:         monetary.Amount(5000),
		Date:          sept4, // Creating transaction on Sept 4th
		PaymentMethod: financialsheet.PaymentMethodOpt1,
		Type:          financialsheet.CategoryTypeCostsOfLiving,
	}

	// Mock expectations
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
	mockRepo.On("FindCategoryByUserAndIdentifier", ctx, "user123", mock.AnythingOfType("financialsheet.CategoryIdentifier")).Return(&financialsheet.Category{
		Identifier: "food",
		Name:       "Food",
		Type:       financialsheet.CategoryTypeCostsOfLiving,
	}, nil)
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time")).Return(nil)

	// Act
	updatedRecord, err := service.CreateTransaction(ctx, record, transaction, false, time.UTC)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, 1, updatedRecord.Points.Current, "Streak should reset to 1 because Sept 3rd was missed")
	assert.Equal(t, 10, updatedRecord.Points.Best, "Best streak should remain 10")
	assert.Len(t, updatedRecord.Points.MissedDays, 1, "Should have one missed day")
	assert.Equal(t, sept2, updatedRecord.Points.MissedDays[0], "Missed day should be Sept 2nd (day after last transaction)")
	assert.Equal(t, sept4, updatedRecord.Points.LastTransactionDate, "LastTransactionDate should be updated to Sept 4th")

	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)
}

// TestAreIntermediateDaysCoveredByNoTransactions tests the helper function directly
func TestAreIntermediateDaysCoveredByNoTransactions(t *testing.T) {
	service := &service{}

	// Test case 1: All intermediate days covered
	lastTxDate := time.Date(2025, 9, 1, 0, 0, 0, 0, time.UTC)
	todayDate := time.Date(2025, 9, 4, 0, 0, 0, 0, time.UTC)
	noTransactionDates := []time.Time{
		time.Date(2025, 9, 2, 0, 0, 0, 0, time.UTC),
		time.Date(2025, 9, 3, 0, 0, 0, 0, time.UTC),
	}

	result := service.areIntermediateDaysCoveredByNoTransactions(lastTxDate, todayDate, noTransactionDates)
	assert.True(t, result, "Should return true when all intermediate days are covered")

	// Test case 2: Missing one intermediate day
	noTransactionDatesPartial := []time.Time{
		time.Date(2025, 9, 2, 0, 0, 0, 0, time.UTC),
		// Missing Sept 3rd
	}

	result = service.areIntermediateDaysCoveredByNoTransactions(lastTxDate, todayDate, noTransactionDatesPartial)
	assert.False(t, result, "Should return false when one intermediate day is missing")

	// Test case 3: Only 1 day difference (no intermediate days)
	todayDateNext := time.Date(2025, 9, 2, 0, 0, 0, 0, time.UTC)

	result = service.areIntermediateDaysCoveredByNoTransactions(lastTxDate, todayDateNext, []time.Time{})
	assert.True(t, result, "Should return true when there are no intermediate days to check")

	// Test case 4: Same day (0 day difference)
	result = service.areIntermediateDaysCoveredByNoTransactions(lastTxDate, lastTxDate, []time.Time{})
	assert.True(t, result, "Should return true when comparing same day")
}

// TestBugReproduction_StreakResetWithNoTransactions reproduces the exact bug scenario
func TestBugReproduction_StreakResetWithNoTransactions(t *testing.T) {
	// Arrange - Reproduce the exact scenario from the bug report
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	// Simulate the exact dates from the bug report
	sept2 := time.Date(2025, 9, 2, 0, 0, 0, 0, time.UTC)
	sept10 := time.Date(2025, 9, 10, 0, 0, 0, 0, time.UTC)

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             13, // Had a best streak of 13
			Best:                13,
			LastTransactionDate: sept2, // Last transaction was on Sept 2nd
			NoTransactionDates: []time.Time{
				sept2, // NoTransaction was recorded on Sept 2nd (this should count as activity)
				time.Date(2025, 9, 3, 0, 0, 0, 0, time.UTC),
				time.Date(2025, 9, 4, 0, 0, 0, 0, time.UTC),
				time.Date(2025, 9, 9, 0, 0, 0, 0, time.UTC),
			},
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	transaction := &financialsheet.Transaction{
		ObjectID:      primitive.NewObjectID(),
		Category:      "food",
		MoneySource:   financialsheet.MoneySourceOpt1,
		Value:         monetary.Amount(5000),
		Date:          sept10, // Creating transaction on Sept 10th
		PaymentMethod: financialsheet.PaymentMethodOpt1,
		Type:          financialsheet.CategoryTypeCostsOfLiving,
	}

	// Mock expectations
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
	mockRepo.On("FindCategoryByUserAndIdentifier", ctx, "user123", mock.AnythingOfType("financialsheet.CategoryIdentifier")).Return(&financialsheet.Category{
		Identifier: "food",
		Name:       "Food",
		Type:       financialsheet.CategoryTypeCostsOfLiving,
	}, nil)
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time")).Return(nil)

	// Act
	updatedRecord, err := service.CreateTransaction(ctx, record, transaction, false, time.UTC)

	// Assert
	assert.NoError(t, err)
	// The streak should be broken because there are gaps not covered by NoTransactions
	// (Sept 5th, 6th, 7th, 8th are missing)
	assert.Equal(t, 1, updatedRecord.Points.Current, "Streak should reset to 1 because there are uncovered missed days")
	assert.Equal(t, 13, updatedRecord.Points.Best, "Best streak should remain 13")
	assert.Len(t, updatedRecord.Points.MissedDays, 1, "Should have one missed day entry")
	assert.Equal(t, sept10, updatedRecord.Points.LastTransactionDate, "LastTransactionDate should be updated to Sept 10th")
}

// TestBugFix_StreakContinuesWithAllDaysCovered shows the fix working correctly
func TestBugFix_StreakContinuesWithAllDaysCovered(t *testing.T) {
	// Arrange - Show that when all intermediate days are covered, streak continues
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	// Simulate scenario where all days between transactions are covered by NoTransactions
	sept1 := time.Date(2025, 9, 1, 0, 0, 0, 0, time.UTC)
	sept5 := time.Date(2025, 9, 5, 0, 0, 0, 0, time.UTC)

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             10, // Had a streak of 10
			Best:                15,
			LastTransactionDate: sept1, // Last transaction was on Sept 1st
			NoTransactionDates: []time.Time{
				time.Date(2025, 9, 2, 0, 0, 0, 0, time.UTC), // All intermediate days covered
				time.Date(2025, 9, 3, 0, 0, 0, 0, time.UTC),
				time.Date(2025, 9, 4, 0, 0, 0, 0, time.UTC),
			},
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	transaction := &financialsheet.Transaction{
		ObjectID:      primitive.NewObjectID(),
		Category:      "food",
		MoneySource:   financialsheet.MoneySourceOpt1,
		Value:         monetary.Amount(5000),
		Date:          sept5, // Creating transaction on Sept 5th
		PaymentMethod: financialsheet.PaymentMethodOpt1,
		Type:          financialsheet.CategoryTypeCostsOfLiving,
	}

	// Mock expectations
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
	mockRepo.On("FindCategoryByUserAndIdentifier", ctx, "user123", mock.AnythingOfType("financialsheet.CategoryIdentifier")).Return(&financialsheet.Category{
		Identifier: "food",
		Name:       "Food",
		Type:       financialsheet.CategoryTypeCostsOfLiving,
	}, nil)
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time")).Return(nil)

	// Act
	updatedRecord, err := service.CreateTransaction(ctx, record, transaction, false, time.UTC)

	// Assert
	assert.NoError(t, err)
	// The streak should continue because all intermediate days are covered by NoTransactions
	assert.Equal(t, 11, updatedRecord.Points.Current, "Streak should continue from 10 to 11 because all intermediate days were covered by NoTransactions")
	assert.Equal(t, 15, updatedRecord.Points.Best, "Best streak should remain 15")
	assert.Empty(t, updatedRecord.Points.MissedDays, "Should not have any missed days since all intermediate days were covered")
	assert.Equal(t, sept5, updatedRecord.Points.LastTransactionDate, "LastTransactionDate should be updated to Sept 5th")
}
