package dashboard

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Note: Financial Independence repository methods are part of the main dashboard repository interface

type FinancialIndependenceTestSuite struct {
	suite.Suite
	mockRepo              *MockDashboardRepository
	mockFinancialSheetSvc *MockFinancialSheetService
	service               Service
	ctx                   context.Context
	userID                string
}

func (suite *FinancialIndependenceTestSuite) SetupTest() {
	suite.mockRepo = new(MockDashboardRepository)
	suite.mockFinancialSheetSvc = new(MockFinancialSheetService)
	suite.service = New(suite.mockRepo, suite.mockFinancialSheetSvc, new(MockGamificationService))
	suite.ctx = context.Background()
	suite.userID = "test-user-id"
}

func TestFinancialIndependenceTestSuite(t *testing.T) {
	suite.Run(t, new(FinancialIndependenceTestSuite))
}

func (suite *FinancialIndependenceTestSuite) TestFindFinancialIndependence_NewUser() {
	// Test case: New user with no existing financial independence data

	// Mock repository calls
	suite.mockRepo.On("FindFinancialIndependenceByUser", suite.ctx, suite.userID).Return(nil, errors.New(errors.Repository, "not found", errors.NotFound, nil))

	// Mock transaction data for calculations
	transactions := []*financialsheet.Transaction{
		{
			Category:    financialsheet.CategoryIdentifierPersonalReserves,
			MoneySource: financialsheet.MoneySourceOpt4, // Investimentos
			Value:       50000,                          // R$ 500.00
			Type:        financialsheet.CategoryTypeCostsOfLiving,
			Date:        time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC),
		},
		{
			Category:    financialsheet.CategoryIdentifierPersonalReserves,
			MoneySource: financialsheet.MoneySourceOpt4, // Investimentos
			Value:       75000,                          // R$ 750.00
			Type:        financialsheet.CategoryTypeCostsOfLiving,
			Date:        time.Date(2024, 2, 15, 0, 0, 0, 0, time.UTC),
		},
		{
			Category:    financialsheet.CategoryIdentifierFood,
			MoneySource: financialsheet.MoneySourceOpt1,
			Value:       30000, // R$ 300.00
			Type:        financialsheet.CategoryTypeCostsOfLiving,
			Date:        time.Date(2024, 1, 10, 0, 0, 0, 0, time.UTC),
		},
	}

	suite.mockFinancialSheetSvc.On("FindAllTransactions", suite.ctx, suite.userID, financialsheet.CategoryTypeExpense, 0, 0, false).Return(transactions, nil)
	// Mock additional calls from FindFinancialMap
	suite.mockFinancialSheetSvc.On("FindAllTransactions", suite.ctx, suite.userID, financialsheet.CategoryTypeCostsOfLiving, mock.AnythingOfType("int"), mock.AnythingOfType("int"), mock.AnythingOfType("bool")).Return([]*financialsheet.Transaction{}, nil)
	suite.mockFinancialSheetSvc.On("FindAllTransactions", suite.ctx, suite.userID, financialsheet.CategoryTypeIncome, mock.AnythingOfType("int"), mock.AnythingOfType("int"), mock.AnythingOfType("bool")).Return([]*financialsheet.Transaction{}, nil)

	// Mock FindStrategicFund call
	strategicFund := &dashboard.StrategicFund{CurrentValue: 100000} // R$ 1,000.00
	suite.mockRepo.On("FindStrategicFund", suite.ctx, suite.userID).Return(strategicFund, nil)

	// Mock financial map for net worth calculation
	financialMap := &dashboard.FinancialMap{
		UserID:           suite.userID,
		StrategicFund:    strategicFund,
		TotalInvestments: 200000, // R$ 2,000.00
		TotalAssets:      300000, // R$ 3,000.00
	}
	suite.mockRepo.On("FindFinancialMap", suite.ctx, suite.userID).Return(financialMap, nil)
	suite.mockRepo.On("CreateFinancialIndependence", suite.ctx, mock.AnythingOfType("*dashboard.FinancialIndependence")).Return(nil)

	// Execute
	result, err := suite.service.FindFinancialIndependence(suite.ctx, suite.userID)

	// Assert
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), suite.userID, result.UserID)
	assert.Equal(suite.T(), monetary.Amount(0), result.RetirementTargetAmount) // Default R$ 0 for new users
	assert.Equal(suite.T(), monetary.Amount(62500), result.MonthlyInvestment)  // Average of 50000 and 75000
	assert.Equal(suite.T(), monetary.Amount(30000), result.MonthlyExpenses)    // Only food expense
	assert.Equal(suite.T(), monetary.Amount(300000), result.CurrentNetWorth)   // 300000
	assert.NotNil(suite.T(), result.FirstTransactionDate)
	assert.Equal(suite.T(), time.Date(2024, 1, 10, 0, 0, 0, 0, time.UTC), *result.FirstTransactionDate)

	suite.mockRepo.AssertExpectations(suite.T())
	suite.mockFinancialSheetSvc.AssertExpectations(suite.T())
}

func (suite *FinancialIndependenceTestSuite) TestFindFinancialIndependence_ExistingUser() {
	// Test case: Existing user with financial independence data

	existingFI := &dashboard.FinancialIndependence{
		ObjectID:               primitive.NewObjectID(),
		UserID:                 suite.userID,
		RetirementTargetAmount: 50000000, // R$ 500,000.00 (custom target)
	}

	suite.mockRepo.On("FindFinancialIndependenceByUser", suite.ctx, suite.userID).Return(existingFI, nil)

	// Mock transaction data
	transactions := []*financialsheet.Transaction{
		{
			Category:    financialsheet.CategoryIdentifierPersonalReserves,
			MoneySource: financialsheet.MoneySourceOpt4,
			Value:       100000,
			Type:        financialsheet.CategoryTypeCostsOfLiving,
			Date:        time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC),
		},
	}

	suite.mockFinancialSheetSvc.On("FindAllTransactions", suite.ctx, suite.userID, financialsheet.CategoryTypeExpense, 0, 0, false).Return(transactions, nil)
	// Mock additional calls from FindFinancialMap
	suite.mockFinancialSheetSvc.On("FindAllTransactions", suite.ctx, suite.userID, financialsheet.CategoryTypeCostsOfLiving, mock.AnythingOfType("int"), mock.AnythingOfType("int"), mock.AnythingOfType("bool")).Return([]*financialsheet.Transaction{}, nil)
	suite.mockFinancialSheetSvc.On("FindAllTransactions", suite.ctx, suite.userID, financialsheet.CategoryTypeIncome, mock.AnythingOfType("int"), mock.AnythingOfType("int"), mock.AnythingOfType("bool")).Return([]*financialsheet.Transaction{}, nil)

	// Mock FindStrategicFund call
	strategicFund := &dashboard.StrategicFund{CurrentValue: 150000}
	suite.mockRepo.On("FindStrategicFund", suite.ctx, suite.userID).Return(strategicFund, nil)

	financialMap := &dashboard.FinancialMap{
		UserID:           suite.userID,
		StrategicFund:    strategicFund,
		TotalInvestments: 250000,
		TotalAssets:      350000,
	}
	suite.mockRepo.On("FindFinancialMap", suite.ctx, suite.userID).Return(financialMap, nil)
	suite.mockRepo.On("UpdateFinancialIndependence", suite.ctx, mock.AnythingOfType("*dashboard.FinancialIndependence")).Return(nil)

	// Execute
	result, err := suite.service.FindFinancialIndependence(suite.ctx, suite.userID)

	// Assert
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), monetary.Amount(50000000), result.RetirementTargetAmount) // Preserved custom target
	assert.Equal(suite.T(), monetary.Amount(100000), result.MonthlyInvestment)
	assert.Equal(suite.T(), monetary.Amount(0), result.MonthlyExpenses) // No non-investment expenses
	assert.Equal(suite.T(), monetary.Amount(400000), result.CurrentNetWorth)

	suite.mockRepo.AssertExpectations(suite.T())
	suite.mockFinancialSheetSvc.AssertExpectations(suite.T())
}

func (suite *FinancialIndependenceTestSuite) TestFindFinancialIndependence_NoInvestments() {
	// Test case: User with no investment transactions should have zero monthly investment contribution

	suite.mockRepo.On("FindFinancialIndependenceByUser", suite.ctx, suite.userID).Return(nil, errors.New(errors.Repository, "not found", errors.NotFound, nil))

	transactions := []*financialsheet.Transaction{
		{
			Category:    financialsheet.CategoryIdentifierFood,
			MoneySource: financialsheet.MoneySourceOpt1,
			Value:       30000,
			Type:        financialsheet.CategoryTypeCostsOfLiving,
		},
	}

	suite.mockFinancialSheetSvc.On("FindAllTransactions", suite.ctx, suite.userID, financialsheet.CategoryTypeExpense, 0, 0, false).Return(transactions, nil)
	// Mock additional calls from FindFinancialMap
	suite.mockFinancialSheetSvc.On("FindAllTransactions", suite.ctx, suite.userID, financialsheet.CategoryTypeCostsOfLiving, mock.AnythingOfType("int"), mock.AnythingOfType("int"), mock.AnythingOfType("bool")).Return([]*financialsheet.Transaction{}, nil)
	suite.mockFinancialSheetSvc.On("FindAllTransactions", suite.ctx, suite.userID, financialsheet.CategoryTypeIncome, mock.AnythingOfType("int"), mock.AnythingOfType("int"), mock.AnythingOfType("bool")).Return([]*financialsheet.Transaction{}, nil)

	// Mock FindStrategicFund call
	strategicFund := &dashboard.StrategicFund{CurrentValue: 0}
	suite.mockRepo.On("FindStrategicFund", suite.ctx, suite.userID).Return(strategicFund, nil)

	financialMap := &dashboard.FinancialMap{
		UserID:           suite.userID,
		StrategicFund:    strategicFund,
		TotalInvestments: 0,
		TotalAssets:      0,
	}
	suite.mockRepo.On("FindFinancialMap", suite.ctx, suite.userID).Return(financialMap, nil)
	suite.mockRepo.On("CreateFinancialIndependence", suite.ctx, mock.AnythingOfType("*dashboard.FinancialIndependence")).Return(nil)

	// Execute
	result, err := suite.service.FindFinancialIndependence(suite.ctx, suite.userID)

	// Assert
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), monetary.Amount(0), result.MonthlyInvestment)

	suite.mockRepo.AssertExpectations(suite.T())
	suite.mockFinancialSheetSvc.AssertExpectations(suite.T())
}

func (suite *FinancialIndependenceTestSuite) TestFindFinancialIndependence_ExcludesIncomeAndCostsOfLivingFromExpenses() {
	// Test case: Ensure income and costs_of_living are excluded from expense calculations

	suite.mockRepo.On("FindFinancialIndependenceByUser", suite.ctx, suite.userID).Return(nil, errors.New(errors.Repository, "not found", errors.NotFound, nil))

	transactions := []*financialsheet.Transaction{
		{
			Category:    financialsheet.CategoryIdentifierCompensation,
			MoneySource: financialsheet.MoneySourceOpt1,
			Value:       100000, // Any income should be excluded from expenses
			Type:        financialsheet.CategoryTypeIncome,
		},
		{
			Category:    financialsheet.CategoryIdentifierPersonalReserves,
			MoneySource: financialsheet.MoneySourceOpt4, // Investment - should be excluded from expenses
			Value:       50000,
			Type:        financialsheet.CategoryTypeCostsOfLiving,
		},
		{
			Category:    financialsheet.CategoryIdentifierFood,
			MoneySource: financialsheet.MoneySourceOpt1,
			Value:       30000, // Any costs_of_living should be excluded in expenses
			Type:        financialsheet.CategoryTypeCostsOfLiving,
		},
		{
			Category:    financialsheet.CategoryIdentifierTransportation,
			MoneySource: financialsheet.MoneySourceOpt1,
			Value:       20000, // Should be included in expenses
			Type:        financialsheet.CategoryTypeExpense,
		},
		{
			Category:    financialsheet.CategoryIdentifierHousing,
			MoneySource: financialsheet.MoneySourceOpt1,
			Value:       5000, // Should be included in expenses
			Type:        financialsheet.CategoryTypeExpense,
		},
	}

	suite.mockFinancialSheetSvc.On("FindAllTransactions", suite.ctx, suite.userID, financialsheet.CategoryType(""), 0, 0, false).Return(transactions, nil)
	// Mock additional calls from FindFinancialMap
	suite.mockFinancialSheetSvc.On("FindAllTransactions", suite.ctx, suite.userID, financialsheet.CategoryTypeCostsOfLiving, mock.AnythingOfType("int"), mock.AnythingOfType("int"), mock.AnythingOfType("bool")).Return([]*financialsheet.Transaction{}, nil)
	suite.mockFinancialSheetSvc.On("FindAllTransactions", suite.ctx, suite.userID, financialsheet.CategoryTypeIncome, mock.AnythingOfType("int"), mock.AnythingOfType("int"), mock.AnythingOfType("bool")).Return([]*financialsheet.Transaction{}, nil)

	// Mock FindStrategicFund call
	strategicFund := &dashboard.StrategicFund{CurrentValue: 0}
	suite.mockRepo.On("FindStrategicFund", suite.ctx, suite.userID).Return(strategicFund, nil)

	financialMap := &dashboard.FinancialMap{
		UserID:           suite.userID,
		StrategicFund:    strategicFund,
		TotalInvestments: 0,
		TotalAssets:      0,
	}
	suite.mockRepo.On("FindFinancialMap", suite.ctx, suite.userID).Return(financialMap, nil)
	suite.mockRepo.On("CreateFinancialIndependence", suite.ctx, mock.AnythingOfType("*dashboard.FinancialIndependence")).Return(nil)

	// Execute
	result, err := suite.service.FindFinancialIndependence(suite.ctx, suite.userID)

	// Assert
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), monetary.Amount(50000), result.MonthlyInvestment) // Investment transaction
	assert.Equal(suite.T(), monetary.Amount(12500), result.MonthlyExpenses)   // Average of 20000 and 5000 excluding income and costs_of_living

	suite.mockRepo.AssertExpectations(suite.T())
	suite.mockFinancialSheetSvc.AssertExpectations(suite.T())
}

func (suite *FinancialIndependenceTestSuite) TestUpdateRetirementTargetAmount_Success() {
	// Test case: Successfully update retirement target amount

	existingFI := &dashboard.FinancialIndependence{
		ObjectID:               primitive.NewObjectID(),
		UserID:                 suite.userID,
		RetirementTargetAmount: 25000000,
	}

	// Mock the FindFinancialIndependenceByUser call (which will be called by UpdateRetirementTargetAmount)
	suite.mockRepo.On("FindFinancialIndependenceByUser", suite.ctx, suite.userID).Return(existingFI, nil)
	suite.mockFinancialSheetSvc.On("FindAllTransactions", suite.ctx, suite.userID, financialsheet.CategoryTypeExpense, 0, 0, false).Return([]*financialsheet.Transaction{}, nil)
	// Mock additional calls from FindFinancialMap
	suite.mockFinancialSheetSvc.On("FindAllTransactions", suite.ctx, suite.userID, financialsheet.CategoryTypeCostsOfLiving, mock.AnythingOfType("int"), mock.AnythingOfType("int"), mock.AnythingOfType("bool")).Return([]*financialsheet.Transaction{}, nil)
	suite.mockFinancialSheetSvc.On("FindAllTransactions", suite.ctx, suite.userID, financialsheet.CategoryTypeIncome, mock.AnythingOfType("int"), mock.AnythingOfType("int"), mock.AnythingOfType("bool")).Return([]*financialsheet.Transaction{}, nil)

	// Mock FindStrategicFund call
	strategicFund := &dashboard.StrategicFund{CurrentValue: 0}
	suite.mockRepo.On("FindStrategicFund", suite.ctx, suite.userID).Return(strategicFund, nil)

	financialMap := &dashboard.FinancialMap{
		UserID:           suite.userID,
		StrategicFund:    strategicFund,
		TotalInvestments: 0,
		TotalAssets:      0,
	}
	suite.mockRepo.On("FindFinancialMap", suite.ctx, suite.userID).Return(financialMap, nil)
	suite.mockRepo.On("UpdateFinancialIndependence", suite.ctx, mock.AnythingOfType("*dashboard.FinancialIndependence")).Return(nil)

	newTargetAmount := monetary.Amount(75000000) // R$ 750,000.00

	// Execute
	err := suite.service.UpdateRetirementTargetAmount(suite.ctx, suite.userID, newTargetAmount)

	// Assert
	assert.NoError(suite.T(), err)

	suite.mockRepo.AssertExpectations(suite.T())
	suite.mockFinancialSheetSvc.AssertExpectations(suite.T())
}

func (suite *FinancialIndependenceTestSuite) TestUpdateRetirementTargetAmount_InvalidAmount() {
	// Test case: Invalid (negative) retirement target amount

	invalidAmount := monetary.Amount(-100000)

	// Execute
	err := suite.service.UpdateRetirementTargetAmount(suite.ctx, suite.userID, invalidAmount)

	// Assert
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "retirement target amount must be positive")
}

func (suite *FinancialIndependenceTestSuite) TestFindFinancialIndependence_NoTransactions() {
	// Test case: User with no transactions should have nil first transaction date

	suite.mockRepo.On("FindFinancialIndependenceByUser", suite.ctx, suite.userID).Return(nil, errors.New(errors.Repository, "not found", errors.NotFound, nil))
	suite.mockFinancialSheetSvc.On("FindAllTransactions", suite.ctx, suite.userID, financialsheet.CategoryTypeExpense, 0, 0, false).Return([]*financialsheet.Transaction{}, nil)
	// Mock additional calls from FindFinancialMap
	suite.mockFinancialSheetSvc.On("FindAllTransactions", suite.ctx, suite.userID, financialsheet.CategoryTypeCostsOfLiving, mock.AnythingOfType("int"), mock.AnythingOfType("int"), mock.AnythingOfType("bool")).Return([]*financialsheet.Transaction{}, nil)
	suite.mockFinancialSheetSvc.On("FindAllTransactions", suite.ctx, suite.userID, financialsheet.CategoryTypeIncome, mock.AnythingOfType("int"), mock.AnythingOfType("int"), mock.AnythingOfType("bool")).Return([]*financialsheet.Transaction{}, nil)

	// Mock FindStrategicFund call
	strategicFund := &dashboard.StrategicFund{CurrentValue: 0}
	suite.mockRepo.On("FindStrategicFund", suite.ctx, suite.userID).Return(strategicFund, nil)

	financialMap := &dashboard.FinancialMap{
		UserID:           suite.userID,
		StrategicFund:    strategicFund,
		TotalInvestments: 0,
		TotalAssets:      0,
	}
	suite.mockRepo.On("FindFinancialMap", suite.ctx, suite.userID).Return(financialMap, nil)
	suite.mockRepo.On("CreateFinancialIndependence", suite.ctx, mock.AnythingOfType("*dashboard.FinancialIndependence")).Return(nil)

	// Execute
	result, err := suite.service.FindFinancialIndependence(suite.ctx, suite.userID)

	// Assert
	assert.NoError(suite.T(), err)
	assert.Nil(suite.T(), result.FirstTransactionDate)

	suite.mockRepo.AssertExpectations(suite.T())
	suite.mockFinancialSheetSvc.AssertExpectations(suite.T())
}
