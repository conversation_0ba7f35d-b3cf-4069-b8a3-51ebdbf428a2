# Auth Status API Documentation

## Overview

The Auth Status API provides a secure endpoint to check the authentication status of a user based on their phone number. This endpoint is protected by the N8nGuard middleware and requires a valid X-API-Key header.

## Endpoint

```
GET /api/auth/status/:phone
```

## Security

- **Middleware**: `N8nGuard` - Validates the `X-API-Key` header
- **Environment Variable**: `N8N_API_KEY` - Must be set with the expected API key value
- **Authentication**: Any request without the correct `X-API-Key` header is rejected with `401 Unauthorized`

## Request

### Headers
```
X-API-Key: <your-api-key>
```

### Parameters
- `phone` (path parameter): The phone number to check status for (e.g., `+1234567890`)

## Response

The API returns a JSON response with the user's authentication status:

### Authenticated User
```json
{
  "status": "AUTHENTICATED",
  "access": "abc-123-def-456"
}
```

### Pending OTP Verification
```json
{
  "status": "PENDING_VERIFICATION"
}
```

### Unknown User
```json
{
  "status": "UNKNOWN"
}
```

## Status Descriptions

- **AUTHENTICATED**: User has a valid, non-expired session token
- **PENDING_VERIFICATION**: User has a pending OTP that hasn't been verified yet
- **UNKNOWN**: No session or pending OTP found for this phone number

## Error Responses

### Missing API Key
```json
{
  "error": "API key header is missing"
}
```
Status Code: `401 Unauthorized`

### Invalid API Key
```json
{
  "error": "invalid API key"
}
```
Status Code: `401 Unauthorized`

### Missing Phone Parameter
```json
{
  "error": "phone parameter is required"
}
```
Status Code: `400 Bad Request`

## Implementation Details

### Database Collections

The endpoint queries two MongoDB collections:

1. **auth.sessions** - Stores user session tokens with expiration times
2. **auth.otps** - Stores pending OTP verifications with expiration times

### Logic Flow

1. **Validate API Key**: Check X-API-Key header against N8N_API_KEY environment variable
2. **Extract Phone**: Get phone number from URL path parameter
3. **Check Session**: Look for valid, non-expired session for the phone number
4. **Check OTP**: If no session, look for valid, non-expired OTP for the phone number
5. **Return Status**: Return appropriate status based on findings

### Models

#### Session Model
```go
type Session struct {
    Phone     string    `json:"phone" bson:"phone"`
    UserID    string    `json:"userId" bson:"userId"`
    Token     string    `json:"token" bson:"token"`
    ExpiresAt time.Time `json:"expiresAt" bson:"expiresAt"`
    CreatedAt time.Time `json:"createdAt" bson:"createdAt"`
    UpdatedAt time.Time `json:"updatedAt" bson:"updatedAt"`
}
```

#### OTP Model
```go
type OTP struct {
    Phone     string    `json:"phone" bson:"phone"`
    Code      string    `json:"code" bson:"code"`
    ExpiresAt time.Time `json:"expiresAt" bson:"expiresAt"`
    Verified  bool      `json:"verified" bson:"verified"`
    CreatedAt time.Time `json:"createdAt" bson:"createdAt"`
    UpdatedAt time.Time `json:"updatedAt" bson:"updatedAt"`
}
```

## Usage Example

```bash
curl -X GET \
  "https://your-api.com/api/auth/status/+1234567890" \
  -H "X-API-Key: your-secret-api-key"
```

## Environment Setup

Make sure to set the following environment variable:

```bash
N8N_API_KEY=your-secret-api-key
```

This key will be used to validate incoming requests to the status endpoint.
