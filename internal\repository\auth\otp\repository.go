package otp

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/auth"
)

type Reader interface {
	FindByPhone(ctx context.Context, phone string) (*auth.OTP, error)
	FindValidByPhone(ctx context.Context, phone string) (*auth.OTP, error)
	FindByPhoneAndCode(ctx context.Context, phone, code string) (*auth.OTP, error)
}

type Writer interface {
	Create(ctx context.Context, otp *auth.OTP) error
	Update(ctx context.Context, otp *auth.OTP) error
	Upsert(ctx context.Context, otp *auth.OTP) error
	Delete(ctx context.Context, id string) error
	DeleteByPhone(ctx context.Context, phone string) error
	DeleteExpired(ctx context.Context) error
}

type Repository interface {
	Reader
	Writer
}
