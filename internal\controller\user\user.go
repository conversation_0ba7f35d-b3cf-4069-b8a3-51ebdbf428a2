package user

import (
	"net/http"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/labstack/echo/v4"
)

// V2 - Self-service endpoints
// CRUD
func (uc *controller) Find() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		user, err := uc.Service.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, user.Sanitize())
	}
}

func (uc *controller) FindByEmail() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		var body map[string]interface{}
		if err := c.Bind(&body); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		emailParam := strings.ToLower(strings.TrimSpace(body["email"].(string)))

		user, err := uc.Service.FindByEmail(ctx, emailParam)
		if err != nil {
			return err
		}

		if userToken.Uid != user.ID {
			return errors.New(errors.Controller, errors.UserForbidden, errors.Forbidden, nil)
		}

		return c.JSON(http.StatusOK, user.Sanitize())
	}
}

func (uc *controller) Patch() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user token from request
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get current user
		currentUser, err := uc.Service.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Bind patch data
		var patchData model.User
		if err := c.Bind(&patchData); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		// Ensure ID is not modified
		if patchData.ID != "" {
			return errors.New(errors.Controller, "user ID cannot be manipulated", errors.Validation, nil)
		}

		// Ensure roles are not modified
		if len(patchData.Roles) > 0 {
			return errors.New(errors.Controller, "user roles cannot be manipulated", errors.Forbidden, nil)
		}

		// Apply patch without running full validation
		if err := uc.Service.Patch(ctx, currentUser, &patchData); err != nil {
			return err
		}

		// Get updated user
		updatedUser, err := uc.Service.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, updatedUser.Sanitize())
	}
}

func (uc *controller) Delete() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		var deleteReason model.DeleteReason
		if err := c.Bind(&deleteReason); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}
		if err := uc.Service.Delete(ctx, userToken.Uid, &deleteReason); err != nil {
			return err
		}
		return c.JSON(http.StatusNoContent, nil)
	}
}

func (uc *controller) LegacyFind() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		user, err := uc.Service.Find(ctx, c.Param("id"))

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, user.Sanitize())
	}
}

func (uc *controller) LegacyUpdate() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		user, err := uc.Service.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		var newUser model.User
		if err = c.Bind(&newUser); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if len(newUser.Roles) > 0 {
			return errors.New(errors.Controller, "user roles cannot be manipulated", errors.Forbidden, nil)
		}

		// Referral code, used referral code, referring user ID, and register source cannot be modified via legacy update
		existingReferralCode := user.ReferralCode
		existingUsedReferralCode := user.UsedReferralCode
		existingReferringUserID := user.ReferringUserID
		existingRegisterSource := user.RegisterSource

		if err = user.PrepareUpdate(&newUser); err != nil {
			return err
		}

		user.ReferralCode = existingReferralCode
		user.UsedReferralCode = existingUsedReferralCode
		user.ReferringUserID = existingReferringUserID
		user.RegisterSource = existingRegisterSource

		if err = uc.Service.Update(ctx, user); err != nil {
			return err
		}

		updatedUser, err := uc.Service.Find(ctx, user.ID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, updatedUser.Sanitize())
	}
}

func (uc *controller) LegacyDelete() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var deleteReason model.DeleteReason

		if err := c.Bind(&deleteReason); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := uc.Service.Delete(ctx, c.Param("id"), &deleteReason); err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}

// Card CRUD
func (uc *controller) FindCard() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		card, err := uc.Service.FindCard(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		if card == nil {
			return c.JSON(http.StatusNoContent, nil)
		} else {
			return c.JSON(http.StatusOK, card)
		}
	}
}
