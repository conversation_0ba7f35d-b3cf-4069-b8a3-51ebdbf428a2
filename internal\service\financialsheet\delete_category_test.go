package financialsheet

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// MockRepository is a mock implementation of the financialsheet repository
type MockRepository struct {
	mock.Mock
}

func (m *MockRepository) Find(ctx context.Context, id primitive.ObjectID) (*financialsheet.Record, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockRepository) FindAll(ctx context.Context) ([]*financialsheet.Record, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*financialsheet.Record), args.Error(1)
}

func (m *MockRepository) FindByUser(ctx context.Context, userID string) (*financialsheet.Record, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*financialsheet.Record), args.Error(1)
}

func (m *MockRepository) FindByUsers(ctx context.Context, userIDs []string) (map[string]*financialsheet.Record, error) {
	args := m.Called(ctx, userIDs)
	return args.Get(0).(map[string]*financialsheet.Record), args.Error(1)
}

func (m *MockRepository) Create(ctx context.Context, record *financialsheet.Record) (string, error) {
	args := m.Called(ctx, record)
	return args.String(0), args.Error(1)
}

func (m *MockRepository) Update(ctx context.Context, record *financialsheet.Record) error {
	args := m.Called(ctx, record)
	return args.Error(0)
}

func (m *MockRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockRepository) FindCategory(ctx context.Context, id primitive.ObjectID) (*financialsheet.Category, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Category), args.Error(1)
}

func (m *MockRepository) FindAllCategories(ctx context.Context) ([]*financialsheet.Category, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*financialsheet.Category), args.Error(1)
}

func (m *MockRepository) FindCategoryByIdentifier(ctx context.Context, identifier financialsheet.CategoryIdentifier) (*financialsheet.Category, error) {
	args := m.Called(ctx, identifier)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Category), args.Error(1)
}

func (m *MockRepository) FindCategoryByUserAndIdentifier(ctx context.Context, userID string, identifier financialsheet.CategoryIdentifier) (*financialsheet.Category, error) {
	args := m.Called(ctx, userID, identifier)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*financialsheet.Category), args.Error(1)
}

func (m *MockRepository) CreateCategory(ctx context.Context, category *financialsheet.Category) (string, error) {
	args := m.Called(ctx, category)
	return args.String(0), args.Error(1)
}

func (m *MockRepository) UpdateCategory(ctx context.Context, category *financialsheet.Category) error {
	args := m.Called(ctx, category)
	return args.Error(0)
}

func (m *MockRepository) DeleteCategory(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockRepository) FindAllTransactions(ctx context.Context, userID string, categoryType financialsheet.CategoryType, year, month int) ([]*financialsheet.Transaction, error) {
	args := m.Called(ctx, userID, categoryType, year, month)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*financialsheet.Transaction), args.Error(1)
}

func (m *MockRepository) FindAllTransactionsMonthsInRange(ctx context.Context, userID string, categoryType financialsheet.CategoryType, startMonth, endMonth time.Time) ([]*financialsheet.Transaction, error) {
	args := m.Called(ctx, userID, categoryType, startMonth, endMonth)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*financialsheet.Transaction), args.Error(1)
}

func (m *MockRepository) FindTransactionMonthsInRange(ctx context.Context, userID string, startMonth, endMonth time.Time) (map[string]bool, error) {
	args := m.Called(ctx, userID, startMonth, endMonth)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]bool), args.Error(1)
}

func TestDeleteCategory_Success(t *testing.T) {
	mockRepo := new(MockRepository)
	service := &service{Repository: mockRepo}

	ctx := context.Background()
	categoryID := primitive.NewObjectID()
	userID := "user123"

	// Mock user category
	userCategory := &financialsheet.Category{
		ObjectID:   categoryID,
		ID:         categoryID.Hex(),
		User:       userID,
		Identifier: "new_category_1",
		Name:       "My Custom Category",
		Type:       financialsheet.CategoryTypeExpense,
	}

	// Setup mocks
	mockRepo.On("FindCategory", ctx, categoryID).Return(userCategory, nil)
	mockRepo.On("FindAllTransactions", ctx, userID, financialsheet.CategoryType(""), 0, 0).Return([]*financialsheet.Transaction{}, nil)
	mockRepo.On("DeleteCategory", ctx, categoryID).Return(nil)

	// Execute
	err := service.DeleteCategory(ctx, categoryID.Hex(), userID)

	// Assert
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)
}

func TestDeleteCategory_SystemCategory_ShouldFail(t *testing.T) {
	mockRepo := new(MockRepository)
	service := &service{Repository: mockRepo}

	ctx := context.Background()
	categoryID := primitive.NewObjectID()
	userID := "user123"

	// Mock system category (empty user field)
	systemCategory := &financialsheet.Category{
		ObjectID:   categoryID,
		ID:         categoryID.Hex(),
		User:       "", // System category
		Identifier: financialsheet.CategoryIdentifierCompensation,
		Name:       "Remuneração",
		Type:       financialsheet.CategoryTypeIncome,
	}

	// Setup mocks
	mockRepo.On("FindCategory", ctx, categoryID).Return(systemCategory, nil)

	// Execute
	err := service.DeleteCategory(ctx, categoryID.Hex(), userID)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "cannot delete system categories")
	mockRepo.AssertExpectations(t)
}

func TestDeleteCategory_NotOwner_ShouldFail(t *testing.T) {
	mockRepo := new(MockRepository)
	service := &service{Repository: mockRepo}

	ctx := context.Background()
	categoryID := primitive.NewObjectID()
	userID := "user123"
	otherUserID := "user456"

	// Mock category belonging to another user
	otherUserCategory := &financialsheet.Category{
		ObjectID:   categoryID,
		ID:         categoryID.Hex(),
		User:       otherUserID, // Different user
		Identifier: "new_category_1",
		Name:       "Other User's Category",
		Type:       financialsheet.CategoryTypeExpense,
	}

	// Setup mocks
	mockRepo.On("FindCategory", ctx, categoryID).Return(otherUserCategory, nil)

	// Execute
	err := service.DeleteCategory(ctx, categoryID.Hex(), userID)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "you can only delete your own categories")
	mockRepo.AssertExpectations(t)
}

func TestDeleteCategory_CategoryInUse_ShouldFail(t *testing.T) {
	mockRepo := new(MockRepository)
	service := &service{Repository: mockRepo}

	ctx := context.Background()
	categoryID := primitive.NewObjectID()
	userID := "user123"

	// Mock user category
	userCategory := &financialsheet.Category{
		ObjectID:   categoryID,
		ID:         categoryID.Hex(),
		User:       userID,
		Identifier: "new_category_1",
		Name:       "My Custom Category",
		Type:       financialsheet.CategoryTypeExpense,
	}

	// Mock transaction using this category
	transaction := &financialsheet.Transaction{
		ObjectID: primitive.NewObjectID(),
		Category: userCategory.Identifier,
		Value:    100.0,
	}

	// Setup mocks
	mockRepo.On("FindCategory", ctx, categoryID).Return(userCategory, nil)
	mockRepo.On("FindAllTransactions", ctx, userID, financialsheet.CategoryType(""), 0, 0).Return([]*financialsheet.Transaction{transaction}, nil)

	// Execute
	err := service.DeleteCategory(ctx, categoryID.Hex(), userID)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "cannot delete category that is being used in transactions")
	mockRepo.AssertExpectations(t)
}

func TestDeleteCategory_InvalidID_ShouldFail(t *testing.T) {
	mockRepo := new(MockRepository)
	service := &service{Repository: mockRepo}

	ctx := context.Background()
	userID := "user123"

	// Execute with invalid ID
	err := service.DeleteCategory(ctx, "invalid-id", userID)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid category ID")
}
