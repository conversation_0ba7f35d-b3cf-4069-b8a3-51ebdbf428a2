package user

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Reader interface {
	// Read operations
	Find(ctx context.Context, id primitive.ObjectID) (*model.User, error)
	FindAll(ctx context.Context) ([]*model.User, error)
	FindAdmins(ctx context.Context) ([]*model.User, error)
	FindByEmail(ctx context.Context, email string) (*model.User, error)
	FindByPhone(ctx context.Context, phone string) (*model.User, error)
	FindByReferral(ctx context.Context, referralCode string) (*model.User, error)
	FindByReferringUserID(ctx context.Context, referringUserID string) ([]*model.User, error)

	// Generic filtering operations
	FindWithFilter(ctx context.Context, filter interface{}) ([]*model.User, error)

	// Financial Profile History operations
	FindFinancialProfileHistory(ctx context.Context, userID string) ([]*model.FinancialProfileHistory, error)
	FindFinancialProfileHistoryByUsers(ctx context.Context, userIDs []string) ([]*model.FinancialProfileHistory, error)

	// Deleted user operations
	FindDeletedByEmail(ctx context.Context, email string) (*model.User, error)
}

type Writer interface {
	// Create operations
	Create(ctx context.Context, user *model.User) (string, error)
	CreateDelete(ctx context.Context, deletedUser *model.DeletedUser) error

	// Update operations
	Update(ctx context.Context, user *model.User) error

	// Financial Profile History operations
	CreateFinancialProfileHistory(ctx context.Context, history *model.FinancialProfileHistory) error

	// Delete operations
	Delete(ctx context.Context, id primitive.ObjectID) error
}

type Repository interface {
	Reader
	Writer
}
