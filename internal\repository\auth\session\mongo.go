package session

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/auth"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type mongoDB struct {
	collection *mongo.Collection
}

func NewMongoDB(database *mongo.Database) Repository {
	return &mongoDB{
		collection: database.Collection(repository.AUTH_SESSIONS_COLLECTION),
	}
}

// Reader methods

func (r *mongoDB) FindByPhone(ctx context.Context, phone string) (*auth.Session, error) {
	var session auth.Session
	filter := bson.M{"phone": phone}

	err := r.collection.FindOne(ctx, filter).Decode(&session)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "session not found", errors.KeyAuthErrorSessionNotFound, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "failed to find session by phone", errors.KeyAuthErrorSessionNotFound, err)
	}

	session.ID = session.ObjectID.Hex()
	return &session, nil
}

func (r *mongoDB) FindByToken(ctx context.Context, token string) (*auth.Session, error) {
	var session auth.Session
	filter := bson.M{"token": token}

	err := r.collection.FindOne(ctx, filter).Decode(&session)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "session not found", errors.KeyAuthErrorSessionNotFound, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "failed to find session by token", errors.KeyAuthErrorSessionNotFound, err)
	}

	session.ID = session.ObjectID.Hex()
	return &session, nil
}

func (r *mongoDB) FindValidByPhone(ctx context.Context, phone string) (*auth.Session, error) {
	var session auth.Session
	filter := bson.M{
		"phone":     phone,
		"expiresAt": bson.M{"$gt": time.Now()},
	}

	err := r.collection.FindOne(ctx, filter).Decode(&session)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "valid session not found", errors.KeyAuthErrorSessionNotFound, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "failed to find valid session by phone", errors.KeyAuthErrorSessionNotFound, err)
	}

	session.ID = session.ObjectID.Hex()
	return &session, nil
}

// Writer methods

func (r *mongoDB) Create(ctx context.Context, session *auth.Session) error {
	if err := session.PrepareCreate(); err != nil {
		return err
	}

	result, err := r.collection.InsertOne(ctx, session)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to create session", errors.KeyAuthErrorSessionNotFound, err)
	}

	session.ObjectID = result.InsertedID.(primitive.ObjectID)
	session.ID = session.ObjectID.Hex()
	return nil
}

func (r *mongoDB) Update(ctx context.Context, session *auth.Session) error {
	objectID, err := primitive.ObjectIDFromHex(session.ID)
	if err != nil {
		return errors.NewValidationError(errors.Repository, "invalid session ID", errors.KeyAuthErrorSessionNotFound, err)
	}

	session.UpdatedAt = time.Now()
	filter := bson.M{"_id": objectID}
	update := bson.M{"$set": session}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to update session", errors.KeyAuthErrorSessionNotFound, err)
	}

	if result.MatchedCount == 0 {
		return errors.NewNotFoundError(errors.Repository, "session not found for update", errors.KeyAuthErrorSessionNotFound, nil)
	}

	return nil
}

func (r *mongoDB) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.NewValidationError(errors.Repository, "invalid session ID", errors.KeyAuthErrorSessionNotFound, err)
	}

	filter := bson.M{"_id": objectID}
	result, err := r.collection.DeleteOne(ctx, filter)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to delete session", errors.KeyAuthErrorSessionNotFound, err)
	}

	if result.DeletedCount == 0 {
		return errors.NewNotFoundError(errors.Repository, "session not found for deletion", errors.KeyAuthErrorSessionNotFound, nil)
	}

	return nil
}

func (r *mongoDB) DeleteByPhone(ctx context.Context, phone string) error {
	filter := bson.M{"phone": phone}
	_, err := r.collection.DeleteMany(ctx, filter)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to delete sessions by phone", errors.KeyAuthErrorSessionNotFound, err)
	}

	return nil
}

func (r *mongoDB) DeleteExpired(ctx context.Context) error {
	filter := bson.M{"expiresAt": bson.M{"$lt": time.Now()}}
	_, err := r.collection.DeleteMany(ctx, filter)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to delete expired sessions", errors.KeyAuthErrorSessionExpired, err)
	}

	return nil
}
