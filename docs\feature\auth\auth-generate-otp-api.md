# Generate OTP API

## Overview

The Generate OTP API creates a secure, one-time-use code for phone authentication, stores it with an expiration time, and sends it via email to the user.

## Endpoint

**POST** `/api/v2/auth/generate-otp`

## Security

This endpoint is **not protected** by the N8nGuard middleware, making it publicly accessible. This design allows it to be called from various sources (like web apps, forgot password flows, etc.) without requiring API key authentication.

## Request

### Headers
- `Content-Type: application/json`

### Body
```json
{
  "phone": "31991445883"
}
```

### Parameters
- `phone` (string, required): The phone number to generate OTP for. Must be between 10-15 characters.

## Response

### Success Response
**Status Code:** `200 OK`

```json
{
  "status": "success",
  "message": "OTP generation process initiated."
}
```

### Error Responses

#### Missing Phone Number
**Status Code:** `400 Bad Request`

```json
{
  "error": "Phone number is required and must be valid."
}
```

#### Invalid Phone Format
**Status Code:** `400 Bad Request`

```json
{
  "error": "Phone number format is invalid."
}
```

#### Internal Server Error
**Status Code:** `500 Internal Server Error`

```json
{
  "error": "OTP generation failed."
}
```

## Backend Logic

When the backend receives a request at `POST /api/v2/auth/generate-otp`, it performs these steps:

1. **Validate Input**: Ensures the phone number is present and in a valid format (10-15 characters)
2. **Generate Secure OTP**: Creates a cryptographically secure 6-digit random number using `crypto/rand`
3. **Hash the OTP**: Uses bcrypt to hash the generated OTP before storage (never stores plain text)
4. **Store the Hashed OTP**: Performs an upsert operation in the database:
   - Overwrites any existing OTP for the same phone number
   - Sets expiration to 5 minutes from creation time
   - Marks as unverified
5. **Send OTP via Email**: Sends the plain text OTP to the user via email
   - **TODO**: Implement SMS sending in the future
6. **Return Success**: Sends the 200 OK response

## Database Schema

The OTP is stored in the `auth.otps` collection with the following structure:

```go
type OTP struct {
    ObjectID  primitive.ObjectID `bson:"_id,omitempty"`
    Phone     string             `bson:"phone"`
    Code      string             `bson:"code"`        // bcrypt hashed
    ExpiresAt time.Time          `bson:"expiresAt"`   // 5 minutes from creation
    Verified  bool               `bson:"verified"`    // false initially
    CreatedAt time.Time          `bson:"createdAt"`
    UpdatedAt time.Time          `bson:"updatedAt"`
}
```

## Security Features

1. **Cryptographically Secure Generation**: Uses `crypto/rand` instead of `math/rand`
2. **Hashed Storage**: OTPs are bcrypt-hashed before database storage
3. **Automatic Expiration**: OTPs expire after 5 minutes
4. **Upsert Logic**: Prevents spam by overwriting existing OTPs for the same phone
5. **No Sensitive Data in Response**: API only confirms initiation, doesn't return the OTP

## Email Template

The OTP is sent via email with the following template:

- **Subject**: "Seu código de verificação - Dinbora"
- **Content**: HTML email with the 6-digit code prominently displayed
- **Security Notice**: Includes warning not to share the code
- **Expiration Notice**: Mentions 5-minute expiration

## Future Enhancements

- **SMS Integration**: Replace email with SMS for better user experience
- **Rate Limiting**: Add rate limiting to prevent abuse
- **Phone Validation**: Implement more sophisticated phone number validation
- **Brevo Templates**: Create dedicated OTP templates in Brevo for better email formatting

## Usage Example

```bash
curl -X POST http://localhost:8080/api/v2/auth/generate-otp \
  -H "Content-Type: application/json" \
  -d '{"phone": "31991445883"}'
```

## Related Endpoints

- `GET /api/v2/auth/status/:phone` - Check authentication status for a phone number
- Future: `POST /api/v2/auth/verify-otp` - Verify the generated OTP
