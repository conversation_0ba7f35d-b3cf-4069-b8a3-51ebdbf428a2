package auth

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
)

func TestStatus_MissingPhone(t *testing.T) {
	// Setup
	e := echo.New()

	// Create a simple controller instance for testing the Status method
	controller := &controller{}

	// Create request without phone parameter
	req := httptest.NewRequest(http.MethodGet, "/api/auth/status/", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	// Execute
	handler := controller.Status()
	err := handler(c)

	// Assert - should return validation error for missing phone
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "phone parameter is required")
}

func TestStatus_WithPhone(t *testing.T) {
	// Setup
	e := echo.New()

	// Create a mock service that implements the required method
	mockService := NewMockAuthService()

	// Create a controller instance with the mock service
	controller := &controller{
		Service: mockService,
	}

	// Create request with phone parameter
	req := httptest.NewRequest(http.MethodGet, "/api/auth/status/+1234567890", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)
	c.SetParamNames("phone")
	c.SetParamValues("+1234567890")

	// Execute
	handler := controller.Status()
	err := handler(c)

	// Assert - should not error since we have a mock service
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code)
}
