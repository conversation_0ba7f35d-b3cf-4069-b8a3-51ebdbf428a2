package auth

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
)

func TestVerifyOTPController(t *testing.T) {
	// Setup
	mockService := NewMockAuthService()
	controller := &controller{
		Service: mockService,
	}

	// First generate an OTP to have something to verify
	mockService.GenerateOTP(nil, "31991445883")

	// Test successful OTP verification
	t.Run("Successful OTP Verification", func(t *testing.T) {
		e := echo.New()
		
		requestBody := map[string]string{
			"phone": "31991445883",
			"code":  "123456",
		}
		
		jsonBody, _ := json.Marshal(requestBody)
		req := httptest.NewRequest(http.MethodPost, "/api/v2/auth/verify-otp", bytes.NewBuffer(jsonBody))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		
		handler := controller.VerifyOTP()
		err := handler(c)
		
		if err != nil {
			t.Fatalf("Handler returned error: %v", err)
		}
		
		if rec.Code != http.StatusOK {
			t.Errorf("Expected status 200, got %d", rec.Code)
		}
		
		var response map[string]interface{}
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		if err != nil {
			t.Fatalf("Failed to unmarshal response: %v", err)
		}
		
		if response["status"] != "success" {
			t.Errorf("Expected status 'success', got %v", response["status"])
		}
		
		if response["message"] != "Verification successful." {
			t.Errorf("Expected message 'Verification successful.', got %v", response["message"])
		}
		
		if response["access"] == nil {
			t.Error("Expected access token in response")
		}
	})

	// Test missing phone number
	t.Run("Missing Phone Number", func(t *testing.T) {
		e := echo.New()
		
		requestBody := map[string]string{
			"code": "123456",
		}
		
		jsonBody, _ := json.Marshal(requestBody)
		req := httptest.NewRequest(http.MethodPost, "/api/v2/auth/verify-otp", bytes.NewBuffer(jsonBody))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		
		handler := controller.VerifyOTP()
		err := handler(c)
		
		if err == nil {
			t.Error("Expected error for missing phone number")
		}
	})

	// Test missing code
	t.Run("Missing Code", func(t *testing.T) {
		e := echo.New()
		
		requestBody := map[string]string{
			"phone": "31991445883",
		}
		
		jsonBody, _ := json.Marshal(requestBody)
		req := httptest.NewRequest(http.MethodPost, "/api/v2/auth/verify-otp", bytes.NewBuffer(jsonBody))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		
		handler := controller.VerifyOTP()
		err := handler(c)
		
		if err == nil {
			t.Error("Expected error for missing code")
		}
	})

	// Test invalid code
	t.Run("Invalid Code", func(t *testing.T) {
		e := echo.New()
		
		// Generate OTP for a different phone
		mockService.GenerateOTP(nil, "31999999999")
		
		requestBody := map[string]string{
			"phone": "31999999999",
			"code":  "wrong-code",
		}
		
		jsonBody, _ := json.Marshal(requestBody)
		req := httptest.NewRequest(http.MethodPost, "/api/v2/auth/verify-otp", bytes.NewBuffer(jsonBody))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		
		handler := controller.VerifyOTP()
		err := handler(c)
		
		if err == nil {
			t.Error("Expected error for invalid code")
		}
	})

	// Test invalid JSON
	t.Run("Invalid JSON", func(t *testing.T) {
		e := echo.New()
		
		req := httptest.NewRequest(http.MethodPost, "/api/v2/auth/verify-otp", bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		
		handler := controller.VerifyOTP()
		err := handler(c)
		
		if err == nil {
			t.Error("Expected error for invalid JSON")
		}
	})

	// Test no OTP generated
	t.Run("No OTP Generated", func(t *testing.T) {
		e := echo.New()
		
		requestBody := map[string]string{
			"phone": "31888888888", // Phone with no OTP generated
			"code":  "123456",
		}
		
		jsonBody, _ := json.Marshal(requestBody)
		req := httptest.NewRequest(http.MethodPost, "/api/v2/auth/verify-otp", bytes.NewBuffer(jsonBody))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		
		handler := controller.VerifyOTP()
		err := handler(c)
		
		if err == nil {
			t.Error("Expected error for phone with no OTP generated")
		}
	})
}
