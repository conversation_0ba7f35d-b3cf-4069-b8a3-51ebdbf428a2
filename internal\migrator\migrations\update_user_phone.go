package migrations

import (
	"context"
	"fmt"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"github.com/nyaruka/phonenumbers"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type UpdateUserPhone struct {
	db *mongo.Database
}

func NewUpdateUserPhone(db *mongo.Database) *UpdateUserPhone {
	return &UpdateUserPhone{
		db: db,
	}
}

func (m *UpdateUserPhone) Name() string {
	return "update_user_phone_20250905_fix000"
}

func (m *UpdateUserPhone) Up(ctx context.Context) error {
	userCollection := m.db.Collection(repository.USERS_COLLECTION)

	cursor, err := userCollection.Find(ctx, bson.D{})
	if err != nil {
		return fmt.Errorf("failed to find users: %w", err)
	}
	defer cursor.Close(ctx)

	successCount := 0
	errorCount := 0

	for cursor.Next(ctx) {
		var user model.User
		if err := cursor.Decode(&user); err != nil {
			log.Printf("Error decoding user: %v", err)
			errorCount++
			continue
		}

		user.ID = user.ObjectID.Hex()

		// Only proceed if the user has a phone number
		if user.Phone != "" {
			normalizedPhone, err := m.normalizePhoneNumber(user.Phone, "BR")
			if err != nil {
				log.Printf("Error normalizing phone number for user %s (%s): %v",
					user.Name, user.ID, err)
				errorCount++
				continue // Skip this user
			}

			// --- FIX: Move the update logic inside the conditional block ---

			// Only update if the normalized number is different from the original
			// This is an extra check to avoid unnecessary writes.
			if user.Phone == normalizedPhone {
				continue
			}

			updateDoc := bson.M{"$set": bson.M{"phone": normalizedPhone}}
			result, err := userCollection.UpdateOne(ctx,
				bson.D{primitive.E{Key: "_id", Value: user.ObjectID}},
				updateDoc,
			)
			if err != nil {
				log.Printf("Error updating user %s: %v", user.ID, err)
				errorCount++
				continue
			}

			if result.ModifiedCount > 0 {
				successCount++
				log.Printf("Updated phone number for user: %s (%s)",
					user.Name, user.ID)
			}
		}
		// If user.Phone was empty, we simply do nothing and move to the next user.
	}

	if err := cursor.Err(); err != nil {
		return fmt.Errorf("cursor iteration error: %w", err)
	}

	log.Printf("User phone number migration completed - Updated: %d, Errors: %d",
		successCount, errorCount)

	return nil
}

func (m *UpdateUserPhone) normalizePhoneNumber(phone, regionCode string) (string, error) {
	num, err := phonenumbers.Parse(phone, regionCode)
	if err != nil {
		return "", err
	}

	if !phonenumbers.IsValidNumber(num) {
		return "", fmt.Errorf("invalid phone number")
	}

	e164Format := phonenumbers.Format(num, phonenumbers.E164)
	return e164Format, nil
}

func (m *UpdateUserPhone) Down(ctx context.Context) error {
	return nil
}
