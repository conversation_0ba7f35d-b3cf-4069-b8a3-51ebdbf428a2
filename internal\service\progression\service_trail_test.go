package progression

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Test progression using a real trail file
func TestProgressionWithRealTrail(t *testing.T) {
	// Create mocks
	mockRepo := new(MockProgressionRepo)
	mockTrailSvc := new(MockTrailSvc)
	mockVaultSvc := new(MockVaultSvc)
	mockCacheSvc := new(MockCacheSvc)
	calculator := NewCalculator(mockTrailSvc)

	// Create service
	service := New(
		mockRepo,
		mockTrailSvc,
		mockVaultSvc,
		mockCacheSvc,
		nil,
	)

	// Create test data
	ctx := context.Background()
	userId := "test-user-id"
	trailId := "67f6ddf3181babca8896e73c" // Using a real trail ID from the migration folder

	// Create a simple trail for testing
	mockTrail := &content.Trail{
		ID:           trailId,
		Name:         "Test Trail",
		Identifier:   "test-trail",
		Level:        1,
		Logo:         "test-logo.png",
		Color:        "#FFFFFF",
		Requirements: []string{},
		Lessons: []*content.Lesson{
			{
				Name:         "Lesson 1",
				Identifier:   "lesson-1",
				Logo:         "lesson-1-logo.png",
				Color:        "#FF0000",
				Order:        1,
				Requirements: []string{},
				Content: []*content.LessonContent{
					{
						Image:       "image-1.png",
						Identifier:  "lesson-1-content-1",
						Description: "First content of lesson 1",
						Next:        "lesson-1-content-2",
					},
					{
						Image:       "image-2.png",
						Identifier:  "lesson-1-content-2",
						Description: "Second content of lesson 1",
						Next:        "coin", // Completion marker
					},
				},
			},
		},
		Challenge: &content.Challenge{
			Name:        "Test Challenge",
			Identifier:  "test-challenge",
			Description: "Test challenge description",
			Logo:        "challenge-logo.png",
			Color:       "#0000FF",
			Locked:      false,
			Phases: []*content.ChallengePhase{
				{
					Name:         "Phase 1",
					Order:        1,
					Identifier:   "phase-1",
					Requirements: []string{},
					Content: []*content.ChallengeContent{
						{
							Image:       "phase-1-image-1.png",
							Identifier:  "phase-1-content-1",
							Description: "First content of phase 1",
							Next:        "phase-1-content-2",
						},
						{
							Image:       "phase-1-image-2.png",
							Identifier:  "phase-1-content-2",
							Description: "Second content of phase 1",
							Next:        "coin", // Completion marker
						},
					},
				},
			},
		},
	}

	// Print some info about the trail for debugging
	t.Logf("Trail ID: %s, Name: %s", mockTrail.ID, mockTrail.Name)
	t.Logf("Number of lessons: %d", len(mockTrail.Lessons))
	t.Logf("Challenge name: %s", mockTrail.Challenge.Name)

	// Create mock vault
	mockVault := &model.Vault{
		ObjectID:  primitive.NewObjectID(),
		User:      userId,
		Coins:     0,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Setup mocks
	mockTrailSvc.On("Find", ctx, trailId).Return(mockTrail, nil)
	disableCache(ctx, mockCacheSvc)

	mockVaultSvc.On("FindByUser", ctx, userId).Return(mockVault, nil)
	mockVaultSvc.On("Reward", ctx, userId, mock.AnythingOfType("int")).Run(func(args mock.Arguments) {
		coins := args.Get(2).(int)
		mockVault.Coins += int64(coins) // Update the mock vault's coins
	}).Return(nil)

	progressionEvents := make([]*progression.ProgressEvent, 0)
	mockRepo.On("GetUserProgress", ctx, userId).Return(func(ctx context.Context, userID string) *progression.ProgressSummary {
		summary, _ := calculator.CalculateProgressFromEvents(ctx, progressionEvents, userID)
		return summary
	}, nil)
	mockRepo.On("GetProgressSummary", ctx, userId).Return(func(ctx context.Context, userID string) *progression.ProgressSummary {
		summary, _ := calculator.CalculateProgressFromEvents(ctx, progressionEvents, userID)
		return summary
	}, nil)
	mockRepo.On("GetUserEvents", ctx, userId, 0).Return(func(ctx context.Context, userID string, limit int64) []*progression.ProgressEvent {
		return progressionEvents
	}, nil)

	// Mock SaveProgressSummary for when calculator saves the computed summary
	mockRepo.On("SaveProgressSummary", ctx, mock.Anything).Return(nil)

	mockRepo.On("CreateEvent", ctx, mock.Anything).Run(func(args mock.Arguments) {
		progressionEvents = append(progressionEvents, args.Get(1).(*progression.ProgressEvent))
	}).Return(nil)
	mockRepo.On("InvalidateProgressSummary", ctx, userId).Return(nil)
	mockVaultSvc.On("Update", ctx, mock.Anything).Return(nil)

	// Test progression through the first lesson and verify rewards
	t.Run("Lesson Progress and Rewards", func(t *testing.T) {
		// Get the first lesson from the trail
		if len(mockTrail.Lessons) == 0 {
			t.Skip("No lessons found in the trail")
		}

		firstLesson := mockTrail.Lessons[0]
		t.Logf("Testing lesson: %s (%s)", firstLesson.Name, firstLesson.Identifier)

		// Check if the lesson has content
		if len(firstLesson.Content) == 0 {
			t.Skip("No content found in the first lesson")
		}

		// Store initial coin count
		initialCoins := mockVault.Coins
		t.Logf("Initial coins: %d", initialCoins)

		// Progress through each content in the lesson
		for i, content := range firstLesson.Content {
			t.Logf("Processing content %d: %s", i, content.Identifier)

			// Determine the next content
			var nextContent string
			if i < len(firstLesson.Content)-1 {
				nextContent = firstLesson.Content[i+1].Identifier
			} else {
				nextContent = "coin" // Last content completes the lesson
			}

			// Create progression body
			progressionBody := &progression.ProgressionBody{
				Trail:   trailId,
				Module:  firstLesson.Identifier,
				Content: content.Identifier,
				Type:    "LESSON",
				Choice: &progression.ModuleContentChoice{
					Identifier: content.Identifier,
					Next:       nextContent,
				},
			}

			err := service.RecordProgress(ctx, userId, progressionBody)
			//progressionEvents = append(progressionEvents, event)
			assert.NoError(t, err)

			// For the last content, verify that rewards were given
			if i == len(firstLesson.Content)-1 {
				// Capture the updated vault to check rewards
				updatedVault := mockVault

				// Verify coins were awarded
				t.Logf("Final coins: %d (initial: %d)", updatedVault.Coins, initialCoins)
				assert.Greater(t, updatedVault.Coins, initialCoins, "User should receive coins for completing the lesson")

				// Verify events were created
				mockRepo.AssertCalled(t, "CreateEvent", ctx, mock.Anything)
				mockRepo.AssertCalled(t, "InvalidateProgressSummary", ctx, userId)
			}
		}
	})

	// Test progression through the challenge if it exists and verify rewards
	t.Run("Challenge Progress and Rewards", func(t *testing.T) {
		if mockTrail.Challenge == nil || len(mockTrail.Challenge.Phases) == 0 {
			t.Skip("No challenge found in the trail")
		}

		t.Logf("Testing challenge: %s (%s)", mockTrail.Challenge.Name, mockTrail.Challenge.Identifier)

		// Progress through the first phase of the challenge
		firstPhase := mockTrail.Challenge.Phases[0]
		t.Logf("Testing challenge phase: %s (%s)", firstPhase.Name, firstPhase.Identifier)

		// Check if the phase has content
		if len(firstPhase.Content) == 0 {
			t.Skip("No content found in the first challenge phase")
		}

		// Log the content structure
		t.Logf("Challenge phase has %d content items", len(firstPhase.Content))
		for i, content := range firstPhase.Content {
			t.Logf("Content %d: ID=%s, Next=%s", i, content.Identifier, content.Next)
		}

		// Store initial coin count
		initialCoins := mockVault.Coins
		t.Logf("Initial coins before challenge: %d", initialCoins)

		for i, content := range firstPhase.Content {
			// Skip if content identifier is empty
			if content.Identifier == "" {
				t.Logf("Skipping content item %d because identifier is empty", i)
				continue
			}

			var nextContent string
			if i < len(firstPhase.Content)-1 {
				nextContent = firstPhase.Content[i+1].Identifier
				if nextContent == "" {
					nextContent = "coin" // Use coin if next identifier is empty
				}
			} else {
				nextContent = "coin" // Last content completes the phase
			}

			t.Logf("Creating progression for challenge content %s with next=%s", content.Identifier, nextContent)

			progressionBody := &progression.ProgressionBody{
				Trail:   trailId,
				Module:  firstPhase.Identifier,
				Content: content.Identifier,
				Type:    "CHALLENGE",
				Choice: &progression.ModuleContentChoice{
					Identifier: content.Identifier,
					Next:       nextContent,
				},
			}

			// Update the current content in the challenge phase
			err := service.RecordProgress(ctx, userId, progressionBody)
			assert.NoError(t, err)

			// For the last content, verify that rewards were given
			if i == len(firstPhase.Content)-1 {
				// Capture the updated vault to check rewards
				updatedVault := mockVault

				// Verify coins were awarded (challenge completion gives 10 coins + 1 coin for trail completion)
				t.Logf("Final coins after challenge: %d (initial: %d)", updatedVault.Coins, initialCoins)
				assert.Equal(t, initialCoins+11, updatedVault.Coins, "User should receive 10 coins for challenge + 1 coin for trail completion")

			}
		}
	})
}

func disableCache(ctx context.Context, mockCacheSvc *MockCacheSvc) {
	// Mock cache operations for
	mockCacheSvc.On("Get", ctx, "progression:summary:test-user-id").Return(nil, false)
	mockCacheSvc.On("Set", ctx, "progression:summary:test-user-id", mock.Anything, time.Hour).Return(nil)
	mockCacheSvc.On("Delete", ctx, "progression:summary:test-user-id").Return(nil)

}
